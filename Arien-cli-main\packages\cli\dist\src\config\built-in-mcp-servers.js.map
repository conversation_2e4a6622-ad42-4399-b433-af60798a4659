{"version": 3, "file": "built-in-mcp-servers.js", "sourceRoot": "", "sources": ["../../../src/config/built-in-mcp-servers.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAGH,OAAO,EAAE,iBAAiB,EAAE,MAAM,0BAA0B,CAAC;AAE7D;;;;;GAKG;AACH,kDAAkD;AAClD,MAAM,CAAC,MAAM,oBAAoB,GAAoC,iBAAiB,CAAC,kBAAkB,EAAE,CAAC;AAE5G;;;GAGG;AACH,MAAM,CAAC,MAAM,wBAAwB,GAAoC,iBAAiB,CAAC,sBAAsB,EAAE,CAAC;AAEpH;;;GAGG;AACH,MAAM,CAAC,KAAK,UAAU,oBAAoB;IACxC,IAAI,CAAC;QACH,mEAAmE;QACnE,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC;QAChD,OAAO,CAAC,GAAG,CAAC,eAAe,GAAG,MAAM,CAAC;QAErC,IAAI,CAAC;YACH,oEAAoE;YACpE,MAAM,iBAAiB,GAAG,MAAM,iBAAiB,CAAC,oBAAoB,EAAE,CAAC;YACzE,OAAO,iBAAiB,CAAC;QAC3B,CAAC;gBAAS,CAAC;YACT,+BAA+B;YAC/B,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;gBAC9B,OAAO,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC;YACrC,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,GAAG,CAAC,eAAe,GAAG,WAAW,CAAC;YAC5C,CAAC;QACH,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,+CAA+C;QAC/C,mEAAmE;QACnE,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YACpE,OAAO,CAAC,IAAI,CAAC,8EAA8E,EAAE,KAAK,CAAC,CAAC;QACtG,CAAC;QACD,OAAO,EAAE,GAAG,oBAAoB,EAAE,CAAC;IACrC,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,wBAAwB;IACtC,OAAO,EAAE,GAAG,oBAAoB,EAAE,CAAC;AACrC,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,yBAAyB;IACvC,OAAO,EAAE,GAAG,wBAAwB,EAAE,CAAC;AACzC,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,yBAAyB;IACvC,OAAO,iBAAiB,CAAC,sBAAsB,EAAE,CAAC;AACpD,CAAC"}