<?xml version="1.0" encoding="UTF-8" ?>
<testsuites name="vitest tests" tests="476" failures="28" errors="0" time="72.759732">
    <testsuite name="src/arien.test.tsx" timestamp="2025-07-02T12:17:37.782Z" hostname="Ajayk" tests="1" failures="0" errors="0" skipped="0" time="0.007464">
        <testcase classname="src/arien.test.tsx" name="gemini.tsx main function &gt; should call process.exit(1) if settings have errors" time="0.0060808">
        </testcase>
    </testsuite>
    <testsuite name="src/nonInteractiveCli.test.ts" timestamp="2025-07-02T12:17:37.783Z" hostname="Ajayk" tests="5" failures="0" errors="0" skipped="0" time="0.026638">
        <testcase classname="src/nonInteractiveCli.test.ts" name="runNonInteractive &gt; should process input and write text output" time="0.0117553">
        </testcase>
        <testcase classname="src/nonInteractiveCli.test.ts" name="runNonInteractive &gt; should handle a single tool call and respond" time="0.0044014">
        </testcase>
        <testcase classname="src/nonInteractiveCli.test.ts" name="runNonInteractive &gt; should handle error during tool execution" time="0.003193">
        </testcase>
        <testcase classname="src/nonInteractiveCli.test.ts" name="runNonInteractive &gt; should exit with error if sendMessageStream throws initially" time="0.0021278">
        </testcase>
        <testcase classname="src/nonInteractiveCli.test.ts" name="runNonInteractive &gt; should not exit if a tool is not found, and should send error back to model" time="0.0025812">
        </testcase>
    </testsuite>
    <testsuite name="src/config/built-in-mcp-servers.test.ts" timestamp="2025-07-02T12:17:37.784Z" hostname="Ajayk" tests="12" failures="7" errors="0" skipped="0" time="0.0438528">
        <testcase classname="src/config/built-in-mcp-servers.test.ts" name="Built-in MCP Servers &gt; getBuiltInMcpServers &gt; should return a copy of built-in MCP servers" time="0.0277609">
            <failure message="expected Promise{…} to deeply equal { …(6) }" type="AssertionError">
AssertionError: expected Promise{…} to deeply equal { …(6) }

- Expected
+ Received

- {
-   &quot;Context 7&quot;: MCPServerConfig {
-     &quot;args&quot;: [
-       &quot;-y&quot;,
-       &quot;@upstash/context7-mcp@latest&quot;,
-     ],
-     &quot;command&quot;: &quot;npx&quot;,
-     &quot;cwd&quot;: undefined,
-     &quot;description&quot;: &quot;Context7 MCP server for enhanced context management&quot;,
-     &quot;env&quot;: undefined,
-     &quot;httpUrl&quot;: undefined,
-     &quot;tcp&quot;: undefined,
-     &quot;timeout&quot;: undefined,
-     &quot;trust&quot;: undefined,
-     &quot;url&quot;: undefined,
-   },
-   &quot;Filesystem&quot;: MCPServerConfig {
-     &quot;args&quot;: [
-       &quot;-y&quot;,
-       &quot;@modelcontextprotocol/server-filesystem@latest&quot;,
-       &quot;--&quot;,
-       &quot;.&quot;,
-     ],
-     &quot;command&quot;: &quot;npx&quot;,
-     &quot;cwd&quot;: &quot;C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\packages\\cli&quot;,
-     &quot;description&quot;: &quot;Filesystem MCP server for file operations&quot;,
-     &quot;env&quot;: undefined,
-     &quot;httpUrl&quot;: undefined,
-     &quot;tcp&quot;: undefined,
-     &quot;timeout&quot;: 5000,
-     &quot;trust&quot;: undefined,
-     &quot;url&quot;: undefined,
-   },
-   &quot;Git&quot;: MCPServerConfig {
-     &quot;args&quot;: [
-       &quot;-y&quot;,
-       &quot;@cyanheads/git-mcp-server&quot;,
-       &quot;--repository&quot;,
-       &quot;.&quot;,
-     ],
-     &quot;command&quot;: &quot;npx&quot;,
-     &quot;cwd&quot;: undefined,
-     &quot;description&quot;: &quot;Git MCP server for version control operations (cyanheads)&quot;,
-     &quot;env&quot;: undefined,
-     &quot;httpUrl&quot;: undefined,
-     &quot;tcp&quot;: undefined,
-     &quot;timeout&quot;: 15000,
-     &quot;trust&quot;: undefined,
-     &quot;url&quot;: undefined,
-   },
-   &quot;Memory&quot;: MCPServerConfig {
-     &quot;args&quot;: [
-       &quot;-y&quot;,
-       &quot;@modelcontextprotocol/server-memory&quot;,
-     ],
-     &quot;command&quot;: &quot;npx&quot;,
-     &quot;cwd&quot;: undefined,
-     &quot;description&quot;: &quot;Memory MCP server for persistent storage&quot;,
-     &quot;env&quot;: undefined,
-     &quot;httpUrl&quot;: undefined,
-     &quot;tcp&quot;: undefined,
-     &quot;timeout&quot;: 8000,
-     &quot;trust&quot;: undefined,
-     &quot;url&quot;: undefined,
-   },
-   &quot;Playwright&quot;: MCPServerConfig {
-     &quot;args&quot;: [
-       &quot;-y&quot;,
-       &quot;@playwright/mcp@latest&quot;,
-     ],
-     &quot;command&quot;: &quot;npx&quot;,
-     &quot;cwd&quot;: undefined,
-     &quot;description&quot;: &quot;Playwright MCP server for browser automation&quot;,
-     &quot;env&quot;: undefined,
-     &quot;httpUrl&quot;: undefined,
-     &quot;tcp&quot;: undefined,
-     &quot;timeout&quot;: undefined,
-     &quot;trust&quot;: undefined,
-     &quot;url&quot;: undefined,
-   },
-   &quot;Sequential Thinking&quot;: MCPServerConfig {
-     &quot;args&quot;: [
-       &quot;-y&quot;,
-       &quot;@modelcontextprotocol/server-sequential-thinking&quot;,
-     ],
-     &quot;command&quot;: &quot;npx&quot;,
-     &quot;cwd&quot;: undefined,
-     &quot;description&quot;: &quot;Sequential Thinking MCP server for dynamic problem-solving&quot;,
-     &quot;env&quot;: undefined,
-     &quot;httpUrl&quot;: undefined,
-     &quot;tcp&quot;: undefined,
-     &quot;timeout&quot;: undefined,
-     &quot;trust&quot;: undefined,
-     &quot;url&quot;: undefined,
-   },
- }
+ Promise {}

 ❯ src/config/built-in-mcp-servers.test.ts:25:23
            </failure>
        </testcase>
        <testcase classname="src/config/built-in-mcp-servers.test.ts" name="Built-in MCP Servers &gt; getBuiltInMcpServers &gt; should include expected built-in servers" time="0.0024047">
            <failure message="expected Promise{…} to have property &quot;Context 7&quot;" type="AssertionError">
AssertionError: expected Promise{…} to have property &quot;Context 7&quot;
 ❯ src/config/built-in-mcp-servers.test.ts:47:25
 ❯ src/config/built-in-mcp-servers.test.ts:46:23
            </failure>
        </testcase>
        <testcase classname="src/config/built-in-mcp-servers.test.ts" name="Built-in MCP Servers &gt; getBuiltInMcpServers &gt; should have valid configurations for all built-in servers" time="0.0008837">
        </testcase>
        <testcase classname="src/config/built-in-mcp-servers.test.ts" name="Built-in MCP Servers &gt; getConfigurableMcpServers &gt; should return a copy of configurable MCP servers" time="0.0010952">
        </testcase>
        <testcase classname="src/config/built-in-mcp-servers.test.ts" name="Built-in MCP Servers &gt; getConfigurableMcpServers &gt; should include expected configurable servers" time="0.0017451">
            <failure message="expected { …(1) } to have property &quot;Web Scraping AI&quot;" type="AssertionError">
AssertionError: expected { …(1) } to have property &quot;Web Scraping AI&quot;
 ❯ src/config/built-in-mcp-servers.test.ts:112:25
 ❯ src/config/built-in-mcp-servers.test.ts:111:23
            </failure>
        </testcase>
        <testcase classname="src/config/built-in-mcp-servers.test.ts" name="Built-in MCP Servers &gt; getConfigurableMcpServers &gt; should have environment variables for servers that require them" time="0.0015074">
            <failure message="Cannot read properties of undefined (reading &apos;env&apos;)" type="TypeError">
TypeError: Cannot read properties of undefined (reading &apos;env&apos;)
 ❯ src/config/built-in-mcp-servers.test.ts:143:36
 ❯ src/config/built-in-mcp-servers.test.ts:142:22
            </failure>
        </testcase>
        <testcase classname="src/config/built-in-mcp-servers.test.ts" name="Built-in MCP Servers &gt; getConfigurableMcpServers &gt; should have placeholder values for API keys" time="0.0009023">
            <failure message="Cannot read properties of undefined (reading &apos;env&apos;)" type="TypeError">
TypeError: Cannot read properties of undefined (reading &apos;env&apos;)
 ❯ src/config/built-in-mcp-servers.test.ts:154:32
            </failure>
        </testcase>
        <testcase classname="src/config/built-in-mcp-servers.test.ts" name="Built-in MCP Servers &gt; Server Configuration Validation &gt; should have valid transport configuration for built-in servers" time="0.0005707">
        </testcase>
        <testcase classname="src/config/built-in-mcp-servers.test.ts" name="Built-in MCP Servers &gt; Server Configuration Validation &gt; should have valid transport configuration for configurable servers" time="0.0004822">
        </testcase>
        <testcase classname="src/config/built-in-mcp-servers.test.ts" name="Built-in MCP Servers &gt; Server Configuration Validation &gt; should use appropriate package managers" time="0.0008241">
        </testcase>
        <testcase classname="src/config/built-in-mcp-servers.test.ts" name="Built-in MCP Servers &gt; Immutability &gt; should not allow modification of returned built-in servers" time="0.001503">
            <failure message="expected Promise{…} to have property &quot;Context 7&quot;" type="AssertionError">
AssertionError: expected Promise{…} to have property &quot;Context 7&quot;
 ❯ src/config/built-in-mcp-servers.test.ts:209:24
            </failure>
        </testcase>
        <testcase classname="src/config/built-in-mcp-servers.test.ts" name="Built-in MCP Servers &gt; Immutability &gt; should not allow modification of returned configurable servers" time="0.0010441">
            <failure message="expected { …(1) } to have property &quot;GitHub&quot;" type="AssertionError">
AssertionError: expected { …(1) } to have property &quot;GitHub&quot;
 ❯ src/config/built-in-mcp-servers.test.ts:220:24
            </failure>
        </testcase>
    </testsuite>
    <testsuite name="src/config/config.integration.test.ts" timestamp="2025-07-02T12:17:37.790Z" hostname="Ajayk" tests="13" failures="0" errors="0" skipped="0" time="0.1193503">
        <testcase classname="src/config/config.integration.test.ts" name="Configuration Integration Tests &gt; File Filtering Configuration &gt; should load default file filtering settings" time="0.073663">
        </testcase>
        <testcase classname="src/config/config.integration.test.ts" name="Configuration Integration Tests &gt; File Filtering Configuration &gt; should load custom file filtering settings from configuration" time="0.0042517">
        </testcase>
        <testcase classname="src/config/config.integration.test.ts" name="Configuration Integration Tests &gt; File Filtering Configuration &gt; should merge user and workspace file filtering settings" time="0.0032077">
        </testcase>
        <testcase classname="src/config/config.integration.test.ts" name="Configuration Integration Tests &gt; Configuration Integration &gt; should handle partial configuration objects gracefully" time="0.0031271">
        </testcase>
        <testcase classname="src/config/config.integration.test.ts" name="Configuration Integration Tests &gt; Configuration Integration &gt; should handle empty configuration objects gracefully" time="0.0029479">
        </testcase>
        <testcase classname="src/config/config.integration.test.ts" name="Configuration Integration Tests &gt; Configuration Integration &gt; should handle missing configuration sections gracefully" time="0.0035353">
        </testcase>
        <testcase classname="src/config/config.integration.test.ts" name="Configuration Integration Tests &gt; Real-world Configuration Scenarios &gt; should handle a security-focused configuration" time="0.0033642">
        </testcase>
        <testcase classname="src/config/config.integration.test.ts" name="Configuration Integration Tests &gt; Real-world Configuration Scenarios &gt; should handle a CI/CD environment configuration" time="0.0038295">
        </testcase>
        <testcase classname="src/config/config.integration.test.ts" name="Configuration Integration Tests &gt; Checkpointing Configuration &gt; should enable checkpointing when the setting is true" time="0.003698">
        </testcase>
        <testcase classname="src/config/config.integration.test.ts" name="Configuration Integration Tests &gt; Checkpointing Configuration &gt; should enable checkpointing by default when no setting is provided" time="0.0032226">
        </testcase>
        <testcase classname="src/config/config.integration.test.ts" name="Configuration Integration Tests &gt; Checkpointing Configuration &gt; should disable checkpointing when explicitly set to false" time="0.0030805">
        </testcase>
        <testcase classname="src/config/config.integration.test.ts" name="Configuration Integration Tests &gt; Extension Context Files &gt; should have an empty array for extension context files by default" time="0.0041089">
        </testcase>
        <testcase classname="src/config/config.integration.test.ts" name="Configuration Integration Tests &gt; Extension Context Files &gt; should correctly store and return extension context file paths" time="0.0033825">
        </testcase>
    </testsuite>
    <testsuite name="src/config/config.test.ts" timestamp="2025-07-02T12:17:37.792Z" hostname="Ajayk" tests="28" failures="3" errors="0" skipped="0" time="47.9185441">
        <testcase classname="src/config/config.test.ts" name="loadCliConfig &gt; should set showMemoryUsage to true when --memory flag is present" time="5.0159491">
            <failure message="Test timed out in 5000ms.
If this is a long-running test, pass a timeout value as the last argument or configure it globally with &quot;testTimeout&quot;." type="Error">
Error: Test timed out in 5000ms.
If this is a long-running test, pass a timeout value as the last argument or configure it globally with &quot;testTimeout&quot;.
 ❯ src/config/config.test.ts:65:3
            </failure>
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig &gt; should set showMemoryUsage to false when --memory flag is not present" time="4.8613131">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig &gt; should set showMemoryUsage to false by default from settings if CLI flag is not present" time="2.0405102">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig &gt; should prioritize CLI flag over settings for showMemoryUsage (CLI true, settings false)" time="1.6554051">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should set telemetry to false by default when no flag or setting is present" time="1.568503">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should set telemetry to true when --telemetry flag is present" time="1.5340456">
            <system-out>
OpenTelemetry SDK started successfully.

            </system-out>
            <system-err>
Accessing resource attributes before async attributes settled

            </system-err>
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should set telemetry to false when --no-telemetry flag is present" time="1.497456">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should use telemetry value from settings if CLI flag is not present (settings true)" time="1.5354366">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should use telemetry value from settings if CLI flag is not present (settings false)" time="1.5507255">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should prioritize --telemetry CLI flag (true) over settings (false)" time="1.4920731">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should prioritize --no-telemetry CLI flag (false) over settings (true)" time="1.4883765">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should use telemetry OTLP endpoint from settings if CLI flag is not present" time="1.5061886">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should prioritize --telemetry-otlp-endpoint CLI flag over settings" time="1.4771826">
            <system-err>
{&quot;stack&quot;:&quot;Error: PeriodicExportingMetricReader: metrics export failed (error Error: 14 UNAVAILABLE: No connection established. Last error: Error: connect ECONNREFUSED 127.0.0.1:4317)\n    at doExport (C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\node_modules\\@opentelemetry\\sdk-metrics\\src\\export\\PeriodicExportingMetricReader.ts:133:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at PeriodicExportingMetricReader._doRun (C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\node_modules\\@opentelemetry\\sdk-metrics\\src\\export\\PeriodicExportingMetricReader.ts:147:7)\n    at PeriodicExportingMetricReader._runOnce (C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\node_modules\\@opentelemetry\\sdk-metrics\\src\\export\\PeriodicExportingMetricReader.ts:104:7)&quot;,&quot;message&quot;:&quot;PeriodicExportingMetricReader: metrics export failed (error Error: 14 UNAVAILABLE: No connection established. Last error: Error: connect ECONNREFUSED 127.0.0.1:4317)&quot;,&quot;name&quot;:&quot;Error&quot;}

            </system-err>
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should use default endpoint if no OTLP endpoint is provided via CLI or settings" time="1.4912987">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should use telemetry target from settings if CLI flag is not present" time="1.4768591">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should prioritize --telemetry-target CLI flag over settings" time="1.4810168">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should use default target if no target is provided via CLI or settings" time="1.4833894">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should use telemetry log prompts from settings if CLI flag is not present" time="1.4761677">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should prioritize --telemetry-log-prompts CLI flag (true) over settings (false)" time="1.4745088">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should prioritize --no-telemetry-log-prompts CLI flag (false) over settings (true)" time="1.4760502">
            <system-err>
{&quot;stack&quot;:&quot;Error: PeriodicExportingMetricReader: metrics export failed (error Error: 14 UNAVAILABLE: No connection established. Last error: Error: connect ECONNREFUSED ::1:4317)\n    at doExport (C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\node_modules\\@opentelemetry\\sdk-metrics\\src\\export\\PeriodicExportingMetricReader.ts:133:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at PeriodicExportingMetricReader._doRun (C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\node_modules\\@opentelemetry\\sdk-metrics\\src\\export\\PeriodicExportingMetricReader.ts:147:7)\n    at PeriodicExportingMetricReader._runOnce (C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\node_modules\\@opentelemetry\\sdk-metrics\\src\\export\\PeriodicExportingMetricReader.ts:104:7)&quot;,&quot;message&quot;:&quot;PeriodicExportingMetricReader: metrics export failed (error Error: 14 UNAVAILABLE: No connection established. Last error: Error: connect ECONNREFUSED ::1:4317)&quot;,&quot;name&quot;:&quot;Error&quot;}

            </system-err>
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should use default log prompts (true) if no value is provided via CLI or settings" time="1.4722817">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="Hierarchical Memory Loading (config.ts) - Placeholder Suite &gt; should pass extension context file paths to loadServerHierarchicalMemory" time="1.4793778">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="mergeMcpServers &gt; should not modify the original settings object" time="1.4762373">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="mergeMcpServers &gt; should include built-in MCP servers by default" time="1.4759412">
            <failure message="expected { …(5) } to have property &quot;Filesystem&quot;" type="AssertionError">
AssertionError: expected { …(5) } to have property &quot;Filesystem&quot;
 ❯ src/config/config.test.ts:362:24
            </failure>
        </testcase>
        <testcase classname="src/config/config.test.ts" name="mergeMcpServers &gt; should allow disabling built-in MCP servers" time="0.0045323">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="mergeMcpServers &gt; should allow user settings to override built-in MCP servers" time="1.4763473">
            <failure message="expected { &apos;Context 7&apos;: { …(2) }, …(4) } to have property &quot;Filesystem&quot;" type="AssertionError">
AssertionError: expected { &apos;Context 7&apos;: { …(2) }, …(4) } to have property &quot;Filesystem&quot;
 ❯ src/config/config.test.ts:408:24
            </failure>
        </testcase>
        <testcase classname="src/config/config.test.ts" name="mergeMcpServers &gt; should allow extension MCP servers to override built-in servers" time="1.4727472">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="mergeMcpServers &gt; should maintain priority order: user &gt; extension &gt; built-in" time="1.4745691">
            <system-err>
{&quot;stack&quot;:&quot;Error: PeriodicExportingMetricReader: metrics export failed (error Error: 14 UNAVAILABLE: No connection established. Last error: Error: connect ECONNREFUSED ::1:4317)\n    at doExport (C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\node_modules\\@opentelemetry\\sdk-metrics\\src\\export\\PeriodicExportingMetricReader.ts:133:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at PeriodicExportingMetricReader._doRun (C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\node_modules\\@opentelemetry\\sdk-metrics\\src\\export\\PeriodicExportingMetricReader.ts:147:7)\n    at PeriodicExportingMetricReader._runOnce (C:\\Users\\<USER>\\OneDrive\\Documents\\Arien-AI\\Arien-cli-main\\node_modules\\@opentelemetry\\sdk-metrics\\src\\export\\PeriodicExportingMetricReader.ts:104:7)&quot;,&quot;message&quot;:&quot;PeriodicExportingMetricReader: metrics export failed (error Error: 14 UNAVAILABLE: No connection established. Last error: Error: connect ECONNREFUSED ::1:4317)&quot;,&quot;name&quot;:&quot;Error&quot;}

            </system-err>
        </testcase>
    </testsuite>
    <testsuite name="src/config/extension.test.ts" timestamp="2025-07-02T12:17:37.798Z" hostname="Ajayk" tests="2" failures="0" errors="0" skipped="0" time="0.0371734">
        <testcase classname="src/config/extension.test.ts" name="loadExtensions &gt; should load context file path when ARIEN.md is present" time="0.023144">
            <system-out>
Loading extension: ext1 (version: 1.0.0)
Loading extension: ext2 (version: 2.0.0)

            </system-out>
        </testcase>
        <testcase classname="src/config/extension.test.ts" name="loadExtensions &gt; should load context file path from the extension config" time="0.0111429">
            <system-out>
Loading extension: ext1 (version: 1.0.0)

            </system-out>
        </testcase>
    </testsuite>
    <testsuite name="src/config/mcp-integration.test.ts" timestamp="2025-07-02T12:17:37.799Z" hostname="Ajayk" tests="6" failures="3" errors="0" skipped="0" time="15.1515971">
        <testcase classname="src/config/mcp-integration.test.ts" name="MCP Integration Tests &gt; Built-in MCP Server Integration &gt; should automatically load built-in MCP servers without any configuration" time="5.0203601">
            <failure message="Test timed out in 5000ms.
If this is a long-running test, pass a timeout value as the last argument or configure it globally with &quot;testTimeout&quot;." type="Error">
Error: Test timed out in 5000ms.
If this is a long-running test, pass a timeout value as the last argument or configure it globally with &quot;testTimeout&quot;.
 ❯ src/config/mcp-integration.test.ts:54:5
            </failure>
        </testcase>
        <testcase classname="src/config/mcp-integration.test.ts" name="MCP Integration Tests &gt; Built-in MCP Server Integration &gt; should respect the enableBuiltInMcpServers setting when disabled" time="0.105516">
        </testcase>
        <testcase classname="src/config/mcp-integration.test.ts" name="MCP Integration Tests &gt; Built-in MCP Server Integration &gt; should allow user configuration to override built-in servers" time="4.7630873">
        </testcase>
        <testcase classname="src/config/mcp-integration.test.ts" name="MCP Integration Tests &gt; Built-in MCP Server Integration &gt; should allow extension configuration to override built-in servers" time="2.036295">
            <failure message="expected { …(6) } to have property &quot;Calculator&quot;" type="AssertionError">
AssertionError: expected { …(6) } to have property &quot;Calculator&quot;
 ❯ src/config/mcp-integration.test.ts:167:26
            </failure>
        </testcase>
        <testcase classname="src/config/mcp-integration.test.ts" name="MCP Integration Tests &gt; Built-in MCP Server Integration &gt; should maintain correct priority: user &gt; extension &gt; built-in" time="1.6543473">
        </testcase>
        <testcase classname="src/config/mcp-integration.test.ts" name="MCP Integration Tests &gt; Built-in MCP Server Integration &gt; should handle mixed configuration scenarios gracefully" time="1.5691525">
            <failure message="expected { …(9) } to have property &quot;Calculator&quot;" type="AssertionError">
AssertionError: expected { …(9) } to have property &quot;Calculator&quot;
 ❯ src/config/mcp-integration.test.ts:272:26
            </failure>
        </testcase>
    </testsuite>
    <testsuite name="src/config/settings.test.ts" timestamp="2025-07-02T12:17:37.801Z" hostname="Ajayk" tests="21" failures="0" errors="0" skipped="0" time="0.0505738">
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should load empty settings if no files exist" time="0.0071644">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should load user settings if only user file exists" time="0.0044609">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should load workspace settings if only workspace file exists" time="0.0021038">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should merge user and workspace settings, with workspace taking precedence" time="0.0024912">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should handle contextFileName correctly when only in user settings" time="0.0018967">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should handle contextFileName correctly when only in workspace settings" time="0.0019616">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should default contextFileName to undefined if not in any settings file" time="0.0017458">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should load telemetry setting from user settings" time="0.0013482">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should load telemetry setting from workspace settings" time="0.0014716">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should prioritize workspace telemetry setting over user setting" time="0.0014116">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should have telemetry as undefined if not in any settings file" time="0.0011594">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should handle JSON parsing errors gracefully" time="0.0036307">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should resolve environment variables in user settings" time="0.0018466">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should resolve environment variables in workspace settings" time="0.0015463">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should prioritize workspace env variables over user env variables if keys clash after resolution" time="0.0013272">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should leave unresolved environment variables as is" time="0.0013998">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should resolve multiple environment variables in a single string" time="0.0010444">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should resolve environment variables in arrays" time="0.0013977">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should correctly pass through null, boolean, and number types, and handle undefined properties" time="0.0031241">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should resolve multiple concatenated environment variables in a single string value" time="0.0021888">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; LoadedSettings class &gt; setValue should update the correct scope and recompute merged settings" time="0.0023576">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/App.test.tsx" timestamp="2025-07-02T12:17:37.804Z" hostname="Ajayk" tests="10" failures="6" errors="0" skipped="0" time="0.9519563">
        <testcase classname="src/ui/App.test.tsx" name="App UI &gt; should display default &quot;ARIEN.md&quot; in footer when contextFileName is not set and count is 1" time="0.2202083">
            <failure message="expected &apos;\n █████╗ ██████╗ ██╗███████╗███╗   █…&apos; to contain &apos;Using 1 ARIEN.md file&apos;" type="AssertionError">
AssertionError: expected &apos;\n █████╗ ██████╗ ██╗███████╗███╗   █…&apos; to contain &apos;Using 1 ARIEN.md file&apos;

- Expected
+ Received

- Using 1 ARIEN.md file
+
+  █████╗ ██████╗ ██╗███████╗███╗   ██╗     █████╗ ██╗
+ ██╔══██╗██╔══██╗██║██╔════╝████╗  ██║    ██╔══██╗██║
+ ███████║██████╔╝██║█████╗  ██╔██╗ ██║    ███████║██║
+ ██╔══██║██╔══██╗██║██╔══╝  ██║╚██╗██║    ██╔══██║██║
+ ██║  ██║██║  ██║██║███████╗██║ ╚████║    ██║  ██║██║
+ ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝╚══════╝╚═╝  ╚═══╝    ╚═╝  ╚═╝╚═╝
+
+
+  Quick Start Tips
+    • Ask questions, edit files, or run commands
+    • Be specific for the best results
+    • Built-in tools available: file operations, web search, browser automation, and more
+    • Type /help for commands and shortcuts
+
+
+  I&apos;m Feeling Lucky (esc to cancel, 0s)
+
+ Context: 1 ARIEN.md file
+
+ /test/dir                 no sandbox (see /docs)                 model (100% context left)
+

 ❯ src/ui/App.test.tsx:244:25
            </failure>
        </testcase>
        <testcase classname="src/ui/App.test.tsx" name="App UI &gt; should display default &quot;ARIEN.md&quot; with plural when contextFileName is not set and count is &gt; 1" time="0.0826132">
            <failure message="expected &apos;\n █████╗ ██████╗ ██╗███████╗███╗   █…&apos; to contain &apos;Using 2 ARIEN.md files&apos;" type="AssertionError">
AssertionError: expected &apos;\n █████╗ ██████╗ ██╗███████╗███╗   █…&apos; to contain &apos;Using 2 ARIEN.md files&apos;

- Expected
+ Received

- Using 2 ARIEN.md files
+
+  █████╗ ██████╗ ██╗███████╗███╗   ██╗     █████╗ ██╗
+ ██╔══██╗██╔══██╗██║██╔════╝████╗  ██║    ██╔══██╗██║
+ ███████║██████╔╝██║█████╗  ██╔██╗ ██║    ███████║██║
+ ██╔══██║██╔══██╗██║██╔══╝  ██║╚██╗██║    ██╔══██║██║
+ ██║  ██║██║  ██║██║███████╗██║ ╚████║    ██║  ██║██║
+ ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝╚══════╝╚═╝  ╚═══╝    ╚═╝  ╚═╝╚═╝
+
+
+  Quick Start Tips
+    • Ask questions, edit files, or run commands
+    • Be specific for the best results
+    • Built-in tools available: file operations, web search, browser automation, and more
+    • Type /help for commands and shortcuts
+
+
+  I&apos;m Feeling Lucky (esc to cancel, 0s)
+
+ Context: 2 ARIEN.md files
+
+ /test/dir                 no sandbox (see /docs)                 model (100% context left)
+

 ❯ src/ui/App.test.tsx:260:25
            </failure>
        </testcase>
        <testcase classname="src/ui/App.test.tsx" name="App UI &gt; should display custom contextFileName in footer when set and count is 1" time="0.058478">
            <failure message="expected &apos;\n █████╗ ██████╗ ██╗███████╗███╗   █…&apos; to contain &apos;Using 1 AGENTS.md file&apos;" type="AssertionError">
AssertionError: expected &apos;\n █████╗ ██████╗ ██╗███████╗███╗   █…&apos; to contain &apos;Using 1 AGENTS.md file&apos;

- Expected
+ Received

- Using 1 AGENTS.md file
+
+  █████╗ ██████╗ ██╗███████╗███╗   ██╗     █████╗ ██╗
+ ██╔══██╗██╔══██╗██║██╔════╝████╗  ██║    ██╔══██╗██║
+ ███████║██████╔╝██║█████╗  ██╔██╗ ██║    ███████║██║
+ ██╔══██║██╔══██╗██║██╔══╝  ██║╚██╗██║    ██╔══██║██║
+ ██║  ██║██║  ██║██║███████╗██║ ╚████║    ██║  ██║██║
+ ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝╚══════╝╚═╝  ╚═══╝    ╚═╝  ╚═╝╚═╝
+
+
+  Quick Start Tips
+    • Ask questions, edit files, or run commands
+    • Be specific for the best results
+    • Built-in tools available: file operations, web search, browser automation, and more
+    • Type /help for commands and shortcuts
+
+
+  I&apos;m Feeling Lucky (esc to cancel, 0s)
+
+ Context: 1 AGENTS.md file
+
+ /test/dir                 no sandbox (see /docs)                 model (100% context left)
+

 ❯ src/ui/App.test.tsx:280:25
            </failure>
        </testcase>
        <testcase classname="src/ui/App.test.tsx" name="App UI &gt; should display a generic message when multiple context files with different names are provided" time="0.0684995">
            <failure message="expected &apos;\n █████╗ ██████╗ ██╗███████╗███╗   █…&apos; to contain &apos;Using 2 context files&apos;" type="AssertionError">
AssertionError: expected &apos;\n █████╗ ██████╗ ██╗███████╗███╗   █…&apos; to contain &apos;Using 2 context files&apos;

- Expected
+ Received

- Using 2 context files
+
+  █████╗ ██████╗ ██╗███████╗███╗   ██╗     █████╗ ██╗
+ ██╔══██╗██╔══██╗██║██╔════╝████╗  ██║    ██╔══██╗██║
+ ███████║██████╔╝██║█████╗  ██╔██╗ ██║    ███████║██║
+ ██╔══██║██╔══██╗██║██╔══╝  ██║╚██╗██║    ██╔══██║██║
+ ██║  ██║██║  ██║██║███████╗██║ ╚████║    ██║  ██║██║
+ ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝╚══════╝╚═╝  ╚═══╝    ╚═╝  ╚═╝╚═╝
+
+
+  Quick Start Tips
+    • Ask questions, edit files, or run commands
+    • Be specific for the best results
+    • Built-in tools available: file operations, web search, browser automation, and more
+    • Type /help for commands and shortcuts
+
+
+  I&apos;m Feeling Lucky (esc to cancel, 0s)
+
+ Context: 2 context files
+
+ /test/dir                 no sandbox (see /docs)                 model (100% context left)
+

 ❯ src/ui/App.test.tsx:300:25
            </failure>
        </testcase>
        <testcase classname="src/ui/App.test.tsx" name="App UI &gt; should display custom contextFileName with plural when set and count is &gt; 1" time="0.1051944">
            <failure message="expected &apos;\n █████╗ ██████╗ ██╗███████╗███╗   █…&apos; to contain &apos;Using 3 MY_NOTES.TXT files&apos;" type="AssertionError">
AssertionError: expected &apos;\n █████╗ ██████╗ ██╗███████╗███╗   █…&apos; to contain &apos;Using 3 MY_NOTES.TXT files&apos;

- Expected
+ Received

- Using 3 MY_NOTES.TXT files
+
+  █████╗ ██████╗ ██╗███████╗███╗   ██╗     █████╗ ██╗
+ ██╔══██╗██╔══██╗██║██╔════╝████╗  ██║    ██╔══██╗██║
+ ███████║██████╔╝██║█████╗  ██╔██╗ ██║    ███████║██║
+ ██╔══██║██╔══██╗██║██╔══╝  ██║╚██╗██║    ██╔══██║██║
+ ██║  ██║██║  ██║██║███████╗██║ ╚████║    ██║  ██║██║
+ ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝╚══════╝╚═╝  ╚═══╝    ╚═╝  ╚═╝╚═╝
+
+
+  Quick Start Tips
+    • Ask questions, edit files, or run commands
+    • Be specific for the best results
+    • Built-in tools available: file operations, web search, browser automation, and more
+    • Type /help for commands and shortcuts
+
+
+  I&apos;m Feeling Lucky (esc to cancel, 0s)
+
+ Context: 3 MY_NOTES.TXT files
+
+ /test/dir                 no sandbox (see /docs)                 model (100% context left)
+

 ❯ src/ui/App.test.tsx:320:25
            </failure>
        </testcase>
        <testcase classname="src/ui/App.test.tsx" name="App UI &gt; should not display context file message if count is 0, even if contextFileName is set" time="0.0440682">
        </testcase>
        <testcase classname="src/ui/App.test.tsx" name="App UI &gt; should display ARIEN.md and MCP server count when both are present" time="0.0691504">
        </testcase>
        <testcase classname="src/ui/App.test.tsx" name="App UI &gt; should display only MCP server count when ARIEN.md count is 0" time="0.0624953">
            <failure message="expected &apos;\n █████╗ ██████╗ ██╗███████╗███╗   █…&apos; to contain &apos;Using 2 MCP servers&apos;" type="AssertionError">
AssertionError: expected &apos;\n █████╗ ██████╗ ██╗███████╗███╗   █…&apos; to contain &apos;Using 2 MCP servers&apos;

- Expected
+ Received

- Using 2 MCP servers
+
+  █████╗ ██████╗ ██╗███████╗███╗   ██╗     █████╗ ██╗
+ ██╔══██╗██╔══██╗██║██╔════╝████╗  ██║    ██╔══██╗██║
+ ███████║██████╔╝██║█████╗  ██╔██╗ ██║    ███████║██║
+ ██╔══██║██╔══██╗██║██╔══╝  ██║╚██╗██║    ██╔══██║██║
+ ██║  ██║██║  ██║██║███████╗██║ ╚████║    ██║  ██║██║
+ ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝╚══════╝╚═╝  ╚═══╝    ╚═╝  ╚═╝╚═╝
+
+
+  Quick Start Tips
+    • Ask questions, edit files, or run commands
+    • Be specific for the best results
+    • Built-in tools available: file operations, web search, browser automation, and more
+    • Create ARIEN.md files to customize your interactions
+    • Type /help for commands and shortcuts
+
+
+  I&apos;m Feeling Lucky (esc to cancel, 0s)
+
+ Context: 2 MCP servers (Ctrl+T to view)
+
+ /test/dir                 no sandbox (see /docs)                 model (100% context left)
+

 ❯ src/ui/App.test.tsx:379:25
            </failure>
        </testcase>
        <testcase classname="src/ui/App.test.tsx" name="App UI &gt; when no theme is set &gt; should display theme dialog if NO_COLOR is not set" time="0.1938819">
        </testcase>
        <testcase classname="src/ui/App.test.tsx" name="App UI &gt; when no theme is set &gt; should display a message if NO_COLOR is set" time="0.0440717">
        </testcase>
    </testsuite>
    <testsuite name="src/utils/startupWarnings.test.ts" timestamp="2025-07-02T12:17:37.808Z" hostname="Ajayk" tests="4" failures="0" errors="0" skipped="4" time="0">
        <testcase classname="src/utils/startupWarnings.test.ts" name="startupWarnings &gt; should return warnings from the file and delete it" time="0">
            <skipped/>
        </testcase>
        <testcase classname="src/utils/startupWarnings.test.ts" name="startupWarnings &gt; should return an empty array if the file does not exist" time="0">
            <skipped/>
        </testcase>
        <testcase classname="src/utils/startupWarnings.test.ts" name="startupWarnings &gt; should return an error message if reading the file fails" time="0">
            <skipped/>
        </testcase>
        <testcase classname="src/utils/startupWarnings.test.ts" name="startupWarnings &gt; should return a warning if deleting the file fails" time="0">
            <skipped/>
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/AuthDialog.test.tsx" timestamp="2025-07-02T12:17:37.809Z" hostname="Ajayk" tests="3" failures="0" errors="0" skipped="0" time="0.352992">
        <testcase classname="src/ui/components/AuthDialog.test.tsx" name="AuthDialog &gt; should show an error if the initial auth type is invalid" time="0.0805074">
        </testcase>
        <testcase classname="src/ui/components/AuthDialog.test.tsx" name="AuthDialog &gt; should prevent exiting when no auth method is selected and show error message" time="0.1480591">
        </testcase>
        <testcase classname="src/ui/components/AuthDialog.test.tsx" name="AuthDialog &gt; should allow exiting when auth method is already selected" time="0.1212847">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/HistoryItemDisplay.test.tsx" timestamp="2025-07-02T12:17:37.809Z" hostname="Ajayk" tests="4" failures="1" errors="0" skipped="0" time="0.2025498">
        <testcase classname="src/ui/components/HistoryItemDisplay.test.tsx" name="&lt;HistoryItemDisplay /&gt; &gt; renders UserMessage for &quot;user&quot; type" time="0.0565267">
        </testcase>
        <testcase classname="src/ui/components/HistoryItemDisplay.test.tsx" name="&lt;HistoryItemDisplay /&gt; &gt; renders StatsDisplay for &quot;stats&quot; type" time="0.0589335">
        </testcase>
        <testcase classname="src/ui/components/HistoryItemDisplay.test.tsx" name="&lt;HistoryItemDisplay /&gt; &gt; renders AboutBox for &quot;about&quot; type" time="0.0178208">
        </testcase>
        <testcase classname="src/ui/components/HistoryItemDisplay.test.tsx" name="&lt;HistoryItemDisplay /&gt; &gt; renders SessionSummaryDisplay for &quot;quit&quot; type" time="0.0660847">
            <failure message="expected &apos;╭────────────────────────────────────…&apos; to contain &apos;Agent powering down. Goodbye!&apos;" type="AssertionError">
AssertionError: expected &apos;╭────────────────────────────────────…&apos; to contain &apos;Agent powering down. Goodbye!&apos;

- Expected
+ Received

- Agent powering down. Goodbye!
+ ╭────────────────────────────────────────────╮
+ │                                            │
+ │              Session Complete              │
+ │        Thank you for using Arien AI        │
+ │                                            │
+ │  ────────────────────────────────────────  │
+ │                                            │
+ │                                            │
+ │  Cumulative Stats (1 Turns)                │
+ │                                            │
+ │  Input Tokens             10               │
+ │  Output Tokens            20               │
+ │  Tool Use Tokens           2               │
+ │  Thoughts Tokens           3               │
+ │  Cached Tokens     5 (16.7%)               │
+ │  ───────────────────────────               │
+ │  Total Tokens             30               │
+ │                                            │
+ │  Total duration (API)  123ms               │
+ │  Total duration (wall)    1s               │
+ │                                            │
+ ╰────────────────────────────────────────────╯

 ❯ src/ui/components/HistoryItemDisplay.test.tsx:99:25
            </failure>
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/InputPrompt.test.tsx" timestamp="2025-07-02T12:17:37.810Z" hostname="Ajayk" tests="5" failures="0" errors="0" skipped="0" time="0.8679775">
        <testcase classname="src/ui/components/InputPrompt.test.tsx" name="InputPrompt &gt; should call shellHistory.getPreviousCommand on up arrow in shell mode" time="0.1723547">
        </testcase>
        <testcase classname="src/ui/components/InputPrompt.test.tsx" name="InputPrompt &gt; should call shellHistory.getNextCommand on down arrow in shell mode" time="0.1342111">
        </testcase>
        <testcase classname="src/ui/components/InputPrompt.test.tsx" name="InputPrompt &gt; should set the buffer text when a shell history command is retrieved" time="0.1414807">
        </testcase>
        <testcase classname="src/ui/components/InputPrompt.test.tsx" name="InputPrompt &gt; should call shellHistory.addCommandToHistory on submit in shell mode" time="0.1430363">
        </testcase>
        <testcase classname="src/ui/components/InputPrompt.test.tsx" name="InputPrompt &gt; should NOT call shell history methods when not in shell mode" time="0.2736425">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/LoadingIndicator.test.tsx" timestamp="2025-07-02T12:17:37.811Z" hostname="Ajayk" tests="10" failures="0" errors="0" skipped="0" time="0.125281">
        <testcase classname="src/ui/components/LoadingIndicator.test.tsx" name="&lt;LoadingIndicator /&gt; &gt; should not render when streamingState is Idle" time="0.0324606">
        </testcase>
        <testcase classname="src/ui/components/LoadingIndicator.test.tsx" name="&lt;LoadingIndicator /&gt; &gt; should render spinner, phrase, and time when streamingState is Responding" time="0.0329708">
        </testcase>
        <testcase classname="src/ui/components/LoadingIndicator.test.tsx" name="&lt;LoadingIndicator /&gt; &gt; should render spinner (static), phrase but no time/cancel when streamingState is WaitingForConfirmation" time="0.0080812">
        </testcase>
        <testcase classname="src/ui/components/LoadingIndicator.test.tsx" name="&lt;LoadingIndicator /&gt; &gt; should display the currentLoadingPhrase correctly" time="0.0070715">
        </testcase>
        <testcase classname="src/ui/components/LoadingIndicator.test.tsx" name="&lt;LoadingIndicator /&gt; &gt; should display the elapsedTime correctly when Responding" time="0.0044745">
        </testcase>
        <testcase classname="src/ui/components/LoadingIndicator.test.tsx" name="&lt;LoadingIndicator /&gt; &gt; should render rightContent when provided" time="0.0054304">
        </testcase>
        <testcase classname="src/ui/components/LoadingIndicator.test.tsx" name="&lt;LoadingIndicator /&gt; &gt; should transition correctly between states using rerender" time="0.0159627">
        </testcase>
        <testcase classname="src/ui/components/LoadingIndicator.test.tsx" name="&lt;LoadingIndicator /&gt; &gt; should display fallback phrase if thought is empty" time="0.0048327">
        </testcase>
        <testcase classname="src/ui/components/LoadingIndicator.test.tsx" name="&lt;LoadingIndicator /&gt; &gt; should display the subject of a thought" time="0.0051721">
        </testcase>
        <testcase classname="src/ui/components/LoadingIndicator.test.tsx" name="&lt;LoadingIndicator /&gt; &gt; should prioritize thought.subject over currentLoadingPhrase" time="0.005145">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/SessionSummaryDisplay.test.tsx" timestamp="2025-07-02T12:17:37.813Z" hostname="Ajayk" tests="2" failures="0" errors="0" skipped="0" time="0.1334977">
        <testcase classname="src/ui/components/SessionSummaryDisplay.test.tsx" name="&lt;SessionSummaryDisplay /&gt; &gt; renders correctly with given stats and duration" time="0.1121986">
        </testcase>
        <testcase classname="src/ui/components/SessionSummaryDisplay.test.tsx" name="&lt;SessionSummaryDisplay /&gt; &gt; renders zero state correctly" time="0.0180271">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/Stats.test.tsx" timestamp="2025-07-02T12:17:37.814Z" hostname="Ajayk" tests="7" failures="0" errors="0" skipped="0" time="0.1159884">
        <testcase classname="src/ui/components/Stats.test.tsx" name="&lt;StatRow /&gt; &gt; renders a label and value" time="0.0541281">
        </testcase>
        <testcase classname="src/ui/components/Stats.test.tsx" name="&lt;StatRow /&gt; &gt; renders with a specific value color" time="0.0036485">
        </testcase>
        <testcase classname="src/ui/components/Stats.test.tsx" name="&lt;StatsColumn /&gt; &gt; renders a stats column with children" time="0.0212692">
        </testcase>
        <testcase classname="src/ui/components/Stats.test.tsx" name="&lt;StatsColumn /&gt; &gt; renders a stats column with a specific width" time="0.0087477">
        </testcase>
        <testcase classname="src/ui/components/Stats.test.tsx" name="&lt;StatsColumn /&gt; &gt; renders a cumulative stats column with percentages" time="0.0091618">
        </testcase>
        <testcase classname="src/ui/components/Stats.test.tsx" name="&lt;StatsColumn /&gt; &gt; hides the tool use row when there are no tool use tokens" time="0.0094679">
        </testcase>
        <testcase classname="src/ui/components/Stats.test.tsx" name="&lt;DurationColumn /&gt; &gt; renders a duration column" time="0.0057496">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/StatsDisplay.test.tsx" timestamp="2025-07-02T12:17:37.815Z" hostname="Ajayk" tests="2" failures="0" errors="0" skipped="0" time="0.1230041">
        <testcase classname="src/ui/components/StatsDisplay.test.tsx" name="&lt;StatsDisplay /&gt; &gt; renders correctly with given stats and duration" time="0.0970516">
        </testcase>
        <testcase classname="src/ui/components/StatsDisplay.test.tsx" name="&lt;StatsDisplay /&gt; &gt; renders zero state correctly" time="0.0220893">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/contexts/SessionContext.test.tsx" timestamp="2025-07-02T12:17:37.815Z" hostname="Ajayk" tests="6" failures="0" errors="0" skipped="0" time="0.1155328">
        <testcase classname="src/ui/contexts/SessionContext.test.tsx" name="SessionStatsContext &gt; should provide the correct initial state" time="0.0348053">
            <system-err>
An update to Root inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() =&gt; {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you&apos;re testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act

            </system-err>
        </testcase>
        <testcase classname="src/ui/contexts/SessionContext.test.tsx" name="SessionStatsContext &gt; should increment turnCount when startNewTurn is called" time="0.0222465">
            <system-err>
An update to Root inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() =&gt; {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you&apos;re testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act
`ReactDOMTestUtils.act` is deprecated in favor of `React.act`. Import `act` from `react` instead of `react-dom/test-utils`. See https://react.dev/warnings/react-dom-test-utils for more info.

            </system-err>
        </testcase>
        <testcase classname="src/ui/contexts/SessionContext.test.tsx" name="SessionStatsContext &gt; should aggregate token usage correctly when addUsage is called" time="0.0043167">
            <system-err>
An update to Root inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() =&gt; {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you&apos;re testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act

            </system-err>
        </testcase>
        <testcase classname="src/ui/contexts/SessionContext.test.tsx" name="SessionStatsContext &gt; should correctly track a full logical turn with multiple API calls" time="0.0050269">
            <system-err>
An update to Root inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() =&gt; {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you&apos;re testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act

            </system-err>
        </testcase>
        <testcase classname="src/ui/contexts/SessionContext.test.tsx" name="SessionStatsContext &gt; should overwrite currentResponse with each API call" time="0.004695">
            <system-err>
An update to Root inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() =&gt; {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you&apos;re testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act

            </system-err>
        </testcase>
        <testcase classname="src/ui/contexts/SessionContext.test.tsx" name="SessionStatsContext &gt; should throw an error when useSessionStats is used outside of a provider" time="0.0400188">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/utils/errorParsing.test.ts" timestamp="2025-07-02T12:17:37.817Z" hostname="Ajayk" tests="11" failures="0" errors="0" skipped="0" time="0.0072329">
        <testcase classname="src/ui/utils/errorParsing.test.ts" name="parseAndFormatApiError &gt; should format a valid API error JSON" time="0.0017718">
        </testcase>
        <testcase classname="src/ui/utils/errorParsing.test.ts" name="parseAndFormatApiError &gt; should format a 429 API error with the default message" time="0.0005075">
        </testcase>
        <testcase classname="src/ui/utils/errorParsing.test.ts" name="parseAndFormatApiError &gt; should format a 429 API error with the personal message" time="0.0002258">
        </testcase>
        <testcase classname="src/ui/utils/errorParsing.test.ts" name="parseAndFormatApiError &gt; should format a 429 API error with the vertex message" time="0.0003261">
        </testcase>
        <testcase classname="src/ui/utils/errorParsing.test.ts" name="parseAndFormatApiError &gt; should return the original message if it is not a JSON error" time="0.0002156">
        </testcase>
        <testcase classname="src/ui/utils/errorParsing.test.ts" name="parseAndFormatApiError &gt; should return the original message for malformed JSON" time="0.0009696">
        </testcase>
        <testcase classname="src/ui/utils/errorParsing.test.ts" name="parseAndFormatApiError &gt; should handle JSON that does not match the ApiError structure" time="0.0003121">
        </testcase>
        <testcase classname="src/ui/utils/errorParsing.test.ts" name="parseAndFormatApiError &gt; should format a nested API error" time="0.0004015">
        </testcase>
        <testcase classname="src/ui/utils/errorParsing.test.ts" name="parseAndFormatApiError &gt; should format a StructuredError" time="0.0001985">
        </testcase>
        <testcase classname="src/ui/utils/errorParsing.test.ts" name="parseAndFormatApiError &gt; should format a 429 StructuredError with the vertex message" time="0.0001937">
        </testcase>
        <testcase classname="src/ui/utils/errorParsing.test.ts" name="parseAndFormatApiError &gt; should handle an unknown error type" time="0.0001685">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/utils/formatters.test.ts" timestamp="2025-07-02T12:17:37.818Z" hostname="Ajayk" tests="14" failures="0" errors="0" skipped="0" time="0.0127556">
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatMemoryUsage &gt; should format bytes into KB" time="0.0031632">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatMemoryUsage &gt; should format bytes into MB" time="0.0004562">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatMemoryUsage &gt; should format bytes into GB" time="0.0003401">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatDuration &gt; should format milliseconds less than a second" time="0.0008688">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatDuration &gt; should format a duration of 0" time="0.0003859">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatDuration &gt; should format an exact number of seconds" time="0.0017022">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatDuration &gt; should format a duration in seconds with one decimal place" time="0.0003323">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatDuration &gt; should format an exact number of minutes" time="0.0003168">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatDuration &gt; should format a duration in minutes and seconds" time="0.000351">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatDuration &gt; should format an exact number of hours" time="0.0002958">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatDuration &gt; should format a duration in hours and seconds" time="0.0002888">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatDuration &gt; should format a duration in hours, minutes, and seconds" time="0.0002965">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatDuration &gt; should handle large durations" time="0.0002774">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatDuration &gt; should handle negative durations" time="0.0002509">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/utils/markdownUtilities.test.ts" timestamp="2025-07-02T12:17:37.820Z" hostname="Ajayk" tests="7" failures="0" errors="0" skipped="0" time="0.0084979">
        <testcase classname="src/ui/utils/markdownUtilities.test.ts" name="markdownUtilities &gt; findLastSafeSplitPoint &gt; should split at the last double newline if not in a code block" time="0.0035704">
        </testcase>
        <testcase classname="src/ui/utils/markdownUtilities.test.ts" name="markdownUtilities &gt; findLastSafeSplitPoint &gt; should return content.length if no safe split point is found" time="0.0004298">
        </testcase>
        <testcase classname="src/ui/utils/markdownUtilities.test.ts" name="markdownUtilities &gt; findLastSafeSplitPoint &gt; should prioritize splitting at 

 over being at the very end of the string if the end is not in a code block" time="0.0003311">
        </testcase>
        <testcase classname="src/ui/utils/markdownUtilities.test.ts" name="markdownUtilities &gt; findLastSafeSplitPoint &gt; should return content.length if the only 

 is inside a code block and the end of content is not" time="0.0003158">
        </testcase>
        <testcase classname="src/ui/utils/markdownUtilities.test.ts" name="markdownUtilities &gt; findLastSafeSplitPoint &gt; should correctly identify the last 

 even if it is followed by text not in a code block" time="0.000272">
        </testcase>
        <testcase classname="src/ui/utils/markdownUtilities.test.ts" name="markdownUtilities &gt; findLastSafeSplitPoint &gt; should return content.length if content is empty" time="0.0002345">
        </testcase>
        <testcase classname="src/ui/utils/markdownUtilities.test.ts" name="markdownUtilities &gt; findLastSafeSplitPoint &gt; should return content.length if content has no newlines and no code blocks" time="0.0002775">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/utils/textUtils.test.ts" timestamp="2025-07-02T12:17:37.821Z" hostname="Ajayk" tests="5" failures="0" errors="0" skipped="0" time="0.0075634">
        <testcase classname="src/ui/utils/textUtils.test.ts" name="textUtils &gt; isBinary &gt; should return true for a buffer containing a null byte" time="0.0033839">
        </testcase>
        <testcase classname="src/ui/utils/textUtils.test.ts" name="textUtils &gt; isBinary &gt; should return false for a buffer containing only text" time="0.0004953">
        </testcase>
        <testcase classname="src/ui/utils/textUtils.test.ts" name="textUtils &gt; isBinary &gt; should return false for an empty buffer" time="0.0003136">
        </testcase>
        <testcase classname="src/ui/utils/textUtils.test.ts" name="textUtils &gt; isBinary &gt; should return false for a null or undefined buffer" time="0.0003087">
        </testcase>
        <testcase classname="src/ui/utils/textUtils.test.ts" name="textUtils &gt; isBinary &gt; should only check the sample size" time="0.0003721">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/atCommandProcessor.test.ts" timestamp="2025-07-02T12:17:37.822Z" hostname="Ajayk" tests="17" failures="1" errors="0" skipped="0" time="0.0988511">
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; should pass through query if no @ command is present" time="0.0081641">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; should pass through original query if only a lone @ symbol is present" time="0.0017944">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; should process a valid text file path" time="0.0059802">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; should process a valid directory path and convert to glob" time="0.0021052">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; should process a valid image file path (as text content for now)" time="0.0013571">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; should handle query with text before and after @command" time="0.0016392">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; should correctly unescape paths with escaped spaces" time="0.0018245">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; should handle multiple @file references" time="0.0024484">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; should handle multiple @file references with interleaved text" time="0.0024183">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; should handle a mix of valid, invalid, and lone @ references" time="0.0266449">
            <failure message="expected &quot;spy&quot; to be called with arguments: [ { paths: [ …(2) ], …(1) }, …(1) ][90m

Received: 

[1m  1st spy call:

[22m[2m  [[22m
[2m    {[22m
[2m      &quot;paths&quot;: [[22m
[2m        &quot;valid1.txt&quot;,[22m
[32m-       &quot;resolved/valid2.actual&quot;,[90m
[31m+       &quot;resolved\\valid2.actual&quot;,[90m
[2m      ],[22m
[2m      &quot;respectGitIgnore&quot;: true,[22m
[2m    },[22m
[2m    AbortSignal {},[22m
[2m  ][22m
[39m[90m

Number of calls: [1m1[22m
[39m" type="AssertionError">
AssertionError: expected &quot;spy&quot; to be called with arguments: [ { paths: [ …(2) ], …(1) }, …(1) ]

Received: 

  1st spy call:

  [
    {
      &quot;paths&quot;: [
        &quot;valid1.txt&quot;,
-       &quot;resolved/valid2.actual&quot;,
+       &quot;resolved\\valid2.actual&quot;,
      ],
      &quot;respectGitIgnore&quot;: true,
    },
    AbortSignal {},
  ]


Number of calls: 1

 ❯ src/ui/hooks/atCommandProcessor.test.ts:455:38
            </failure>
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; should return original query if all @paths are invalid or lone @" time="0.0126983">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; should process a file path case-insensitively" time="0.0099533">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; git-aware filtering &gt; should skip git-ignored files in @ commands" time="0.0024594">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; git-aware filtering &gt; should process non-git-ignored files normally" time="0.0036459">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; git-aware filtering &gt; should handle mixed git-ignored and valid files" time="0.0041792">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; git-aware filtering &gt; should always ignore .git directory files" time="0.0023644">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; when recursive file search is disabled &gt; should not use glob search for a nonexistent file" time="0.0063461">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/shellCommandProcessor.test.ts" timestamp="2025-07-02T12:17:37.825Z" hostname="Ajayk" tests="3" failures="0" errors="0" skipped="0" time="0.0629682">
        <testcase classname="src/ui/hooks/shellCommandProcessor.test.ts" name="useShellCommandProcessor &gt; should execute a command and update history on success" time="0.0427823">
        </testcase>
        <testcase classname="src/ui/hooks/shellCommandProcessor.test.ts" name="useShellCommandProcessor &gt; should handle binary output" time="0.0090005">
        </testcase>
        <testcase classname="src/ui/hooks/shellCommandProcessor.test.ts" name="useShellCommandProcessor &gt; should handle command failure" time="0.0075366">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/slashCommandProcessor.test.ts" timestamp="2025-07-02T12:17:37.825Z" hostname="Ajayk" tests="30" failures="2" errors="0" skipped="0" time="0.3994015">
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /memory add &gt; should return tool scheduling info on valid input" time="0.0382363">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /memory add &gt; should show usage error and return true if no text is provided" time="0.0062051">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /memory show &gt; should call the showMemoryAction and return true" time="0.034758">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /memory refresh &gt; should call performMemoryRefresh and return true" time="0.0060524">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; Unknown /memory subcommand &gt; should show an error for unknown /memory subcommand and return true" time="0.005661">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /stats command &gt; should show detailed session statistics" time="0.006496">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /about command &gt; should show the about box with all details including auth and project" time="0.0063287">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /about command &gt; should show sandbox-exec profile when applicable" time="0.0058821">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; Other commands &gt; /help should open help and return true" time="0.0105488">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; Other commands &gt; /clear should clear items, reset chat, and refresh static" time="0.0062598">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; Other commands &gt; /editor should open editor dialog and return true" time="0.0047532">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /bug command &gt; should call open with the correct GitHub issue URL and return true" time="0.0086837">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /bug command &gt; should use the custom bug command URL from config if available" time="0.0092399">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /quit and /exit commands &gt; should handle /quit, set quitting messages, and exit the process" time="0.0179746">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /quit and /exit commands &gt; should handle /exit, set quitting messages, and exit the process" time="0.0073704">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; Unknown command &gt; should show an error and return true for a general unknown command" time="0.0044213">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /tools command &gt; should show an error if tool registry is not available" time="0.0053995">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /tools command &gt; should show an error if getAllTools returns undefined" time="0.0091535">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /tools command &gt; should display only Arien CLI tools (filtering out MCP tools)" time="0.0127275">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /tools command &gt; should display a message when no Arien CLI tools are available" time="0.0084519">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /tools command &gt; should display tool descriptions when /tools desc is used" time="0.0102996">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /mcp command &gt; should show an error if tool registry is not available" time="0.0055122">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /mcp command &gt; should display a message with a URL when no MCP servers are configured in a sandbox" time="0.0324867">
            <failure message="expected 2nd &quot;spy&quot; call to have been called with [ ObjectContaining{…}, Any&lt;Number&gt; ]" type="AssertionError">
AssertionError: expected 2nd &quot;spy&quot; call to have been called with [ ObjectContaining{…}, Any&lt;Number&gt; ]

- Expected
+ Received

  [
-   ObjectContaining {
-     &quot;text&quot;: &quot;No MCP servers configured. Please open the following URL in your browser to view documentation:
+   {
+     &quot;text&quot;: &quot;No MCP servers found. This is unusual as Arien AI includes 12 built-in MCP servers by default. Built-in servers may be disabled in your settings. Please open the following URL in your browser to view documentation:
  https://goo.gle/gemini-cli-docs-mcp&quot;,
      &quot;type&quot;: &quot;info&quot;,
    },
    Any&lt;Number&gt;,
  ]

 ❯ src/ui/hooks/slashCommandProcessor.test.ts:835:27
            </failure>
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /mcp command &gt; should display a message and open a URL when no MCP servers are configured outside a sandbox" time="0.0106267">
            <failure message="expected 2nd &quot;spy&quot; call to have been called with [ ObjectContaining{…}, Any&lt;Number&gt; ]" type="AssertionError">
AssertionError: expected 2nd &quot;spy&quot; call to have been called with [ ObjectContaining{…}, Any&lt;Number&gt; ]

- Expected
+ Received

  [
-   ObjectContaining {
-     &quot;text&quot;: &quot;No MCP servers configured. Opening documentation in your browser: https://goo.gle/gemini-cli-docs-mcp&quot;,
+   {
+     &quot;text&quot;: &quot;No MCP servers found. This is unusual as Arien AI includes 12 built-in MCP servers by default. Built-in servers may be disabled in your settings. Please open the following URL in your browser to view documentation:
+ https://goo.gle/gemini-cli-docs-mcp&quot;,
      &quot;type&quot;: &quot;info&quot;,
    },
    Any&lt;Number&gt;,
  ]

 ❯ src/ui/hooks/slashCommandProcessor.test.ts:862:27
            </failure>
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /mcp command &gt; should display configured MCP servers with status indicators and their tools" time="0.021213">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /mcp command &gt; should display tool descriptions when showToolDescriptions is true" time="0.0061068">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /mcp command &gt; should indicate when a server has no tools" time="0.006838">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /mcp command &gt; should show startup indicator when servers are connecting" time="0.0292451">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /mcp schema &gt; should display tool schemas and descriptions" time="0.0115014">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /compress command &gt; should call tryCompressChat(true)" time="0.0445156">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/useArienStream.test.tsx" timestamp="2025-07-02T12:17:37.830Z" hostname="Ajayk" tests="24" failures="0" errors="0" skipped="0" time="0.4850517">
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="mergePartListUnions &gt; should merge multiple PartListUnion arrays" time="0.0042331">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="mergePartListUnions &gt; should handle empty arrays in the input list" time="0.0006479">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="mergePartListUnions &gt; should handle a single PartListUnion array" time="0.0003796">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="mergePartListUnions &gt; should return an empty array if all input arrays are empty" time="0.0003831">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="mergePartListUnions &gt; should handle input list being empty" time="0.0003341">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="mergePartListUnions &gt; should correctly merge when PartListUnion items are single Parts not in arrays" time="0.0004225">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="mergePartListUnions &gt; should handle a mix of arrays and single parts, including empty arrays and undefined/null parts if they were possible (though PartListUnion typing restricts this)" time="0.0004527">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="mergePartListUnions &gt; should preserve the order of parts from the input arrays" time="0.0004113">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="mergePartListUnions &gt; should handle cases where some PartListUnion items are single Parts and others are arrays of Parts" time="0.0004896">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="useArienStream &gt; should not submit tool responses if not all tool calls are completed" time="0.0368226">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="useArienStream &gt; should submit tool responses when all tool calls are completed and ready" time="0.023008">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="useArienStream &gt; should handle all tool calls being cancelled" time="0.0154803">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="useArienStream &gt; Session Stats Integration &gt; should call startNewTurn and addUsage for a simple prompt" time="0.1346573">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="useArienStream &gt; Session Stats Integration &gt; should only call addUsage for a tool continuation prompt" time="0.0066986">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="useArienStream &gt; Session Stats Integration &gt; should not call addUsage if the stream contains no usage metadata" time="0.0190903">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="useArienStream &gt; Session Stats Integration &gt; should not call startNewTurn for a slash command" time="0.0273966">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="useArienStream &gt; should not flicker streaming state to Idle between tool completion and submission" time="0.0303281">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="useArienStream &gt; User Cancellation &gt; should cancel an in-progress stream when escape is pressed" time="0.0422522">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="useArienStream &gt; User Cancellation &gt; should not do anything if escape is pressed when not responding" time="0.005835">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="useArienStream &gt; User Cancellation &gt; should prevent further processing after cancellation" time="0.0853377">
            <system-err>
An update to TestComponent inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() =&gt; {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you&apos;re testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act

            </system-err>
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="useArienStream &gt; User Cancellation &gt; should not cancel if a tool call is in progress (not just responding)" time="0.0053202">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="useArienStream &gt; Client-Initiated Tool Calls &gt; should execute a client-initiated tool without sending a response to AI" time="0.0138734">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="useArienStream &gt; Memory Refresh on save_memory &gt; should call performMemoryRefresh when a save_memory tool call completes successfully" time="0.0104564">
        </testcase>
        <testcase classname="src/ui/hooks/useArienStream.test.tsx" name="useArienStream &gt; Error Handling &gt; should call parseAndFormatApiError with the correct authType on stream initialization failure" time="0.0159547">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/useAutoAcceptIndicator.test.ts" timestamp="2025-07-02T12:17:37.834Z" hostname="Ajayk" tests="6" failures="0" errors="0" skipped="0" time="0.0726585">
        <testcase classname="src/ui/hooks/useAutoAcceptIndicator.test.ts" name="useAutoAcceptIndicator &gt; should initialize with ApprovalMode.AUTO_EDIT if config.getApprovalMode returns ApprovalMode.AUTO_EDIT" time="0.0300933">
        </testcase>
        <testcase classname="src/ui/hooks/useAutoAcceptIndicator.test.ts" name="useAutoAcceptIndicator &gt; should initialize with ApprovalMode.DEFAULT if config.getApprovalMode returns ApprovalMode.DEFAULT" time="0.0051329">
        </testcase>
        <testcase classname="src/ui/hooks/useAutoAcceptIndicator.test.ts" name="useAutoAcceptIndicator &gt; should initialize with ApprovalMode.YOLO if config.getApprovalMode returns ApprovalMode.YOLO" time="0.0045878">
        </testcase>
        <testcase classname="src/ui/hooks/useAutoAcceptIndicator.test.ts" name="useAutoAcceptIndicator &gt; should toggle the indicator and update config when Shift+Tab or Ctrl+Y is pressed" time="0.0174274">
        </testcase>
        <testcase classname="src/ui/hooks/useAutoAcceptIndicator.test.ts" name="useAutoAcceptIndicator &gt; should not toggle if only one key or other keys combinations are pressed" time="0.0057976">
        </testcase>
        <testcase classname="src/ui/hooks/useAutoAcceptIndicator.test.ts" name="useAutoAcceptIndicator &gt; should update indicator when config value changes externally (useEffect dependency)" time="0.0057825">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/useCompletion.integration.test.ts" timestamp="2025-07-02T12:17:37.834Z" hostname="Ajayk" tests="9" failures="3" errors="0" skipped="0" time="1.535543">
        <testcase classname="src/ui/hooks/useCompletion.integration.test.ts" name="useCompletion git-aware filtering integration &gt; should filter git-ignored entries from @ completions" time="0.204221">
        </testcase>
        <testcase classname="src/ui/hooks/useCompletion.integration.test.ts" name="useCompletion git-aware filtering integration &gt; should filter git-ignored directories from @ completions" time="0.1660113">
        </testcase>
        <testcase classname="src/ui/hooks/useCompletion.integration.test.ts" name="useCompletion git-aware filtering integration &gt; should handle recursive search with git-aware filtering" time="0.1719449">
            <system-err>
Error fetching completion suggestions for t: Cannot read properties of undefined (reading &apos;map&apos;)

            </system-err>
        </testcase>
        <testcase classname="src/ui/hooks/useCompletion.integration.test.ts" name="useCompletion git-aware filtering integration &gt; should not perform recursive search when disabled in config" time="0.188158">
            <failure message="expected &quot;readdir&quot; to be called with arguments: [ &apos;/test/project&apos;, …(1) ][90m

Received: 

[1m  1st readdir call:

[22m[2m  [[22m
[32m-   &quot;/test/project&quot;,[90m
[31m+   &quot;C:\\test\\project&quot;,[90m
[2m    {[22m
[2m      &quot;withFileTypes&quot;: true,[22m
[2m    },[22m
[2m  ][22m
[39m[90m

Number of calls: [1m1[22m
[39m" type="AssertionError">
AssertionError: expected &quot;readdir&quot; to be called with arguments: [ &apos;/test/project&apos;, …(1) ]

Received: 

  1st readdir call:

  [
-   &quot;/test/project&quot;,
+   &quot;C:\\test\\project&quot;,
    {
      &quot;withFileTypes&quot;: true,
    },
  ]


Number of calls: 1

 ❯ src/ui/hooks/useCompletion.integration.test.ts:212:24
            </failure>
        </testcase>
        <testcase classname="src/ui/hooks/useCompletion.integration.test.ts" name="useCompletion git-aware filtering integration &gt; should work without config (fallback behavior)" time="0.1574823">
        </testcase>
        <testcase classname="src/ui/hooks/useCompletion.integration.test.ts" name="useCompletion git-aware filtering integration &gt; should handle git discovery service initialization failure gracefully" time="0.1557218">
        </testcase>
        <testcase classname="src/ui/hooks/useCompletion.integration.test.ts" name="useCompletion git-aware filtering integration &gt; should handle directory-specific completions with git filtering" time="0.1573131">
        </testcase>
        <testcase classname="src/ui/hooks/useCompletion.integration.test.ts" name="useCompletion git-aware filtering integration &gt; should use glob for top-level @ completions when available" time="0.1615493">
            <failure message="expected [ { label: &apos;README.md&apos;, …(1) }, …(1) ] to deeply equal [ { label: &apos;README.md&apos;, …(1) }, …(1) ]" type="AssertionError">
AssertionError: expected [ { label: &apos;README.md&apos;, …(1) }, …(1) ] to deeply equal [ { label: &apos;README.md&apos;, …(1) }, …(1) ]

- Expected
+ Received

@@ -2,9 +2,9 @@
    {
      &quot;label&quot;: &quot;README.md&quot;,
      &quot;value&quot;: &quot;README.md&quot;,
    },
    {
-     &quot;label&quot;: &quot;src/index.ts&quot;,
-     &quot;value&quot;: &quot;src/index.ts&quot;,
+     &quot;label&quot;: &quot;src\\index.ts&quot;,
+     &quot;value&quot;: &quot;src\\index.ts&quot;,
    },
  ]

 ❯ src/ui/hooks/useCompletion.integration.test.ts:309:40
            </failure>
        </testcase>
        <testcase classname="src/ui/hooks/useCompletion.integration.test.ts" name="useCompletion git-aware filtering integration &gt; should include dotfiles in glob search when input starts with a dot" time="0.1694041">
            <failure message="expected [ …(3) ] to deeply equal [ …(3) ]" type="AssertionError">
AssertionError: expected [ …(3) ] to deeply equal [ …(3) ]

- Expected
+ Received

@@ -6,9 +6,9 @@
    {
      &quot;label&quot;: &quot;.gitignore&quot;,
      &quot;value&quot;: &quot;.gitignore&quot;,
    },
    {
-     &quot;label&quot;: &quot;src/index.ts&quot;,
-     &quot;value&quot;: &quot;src/index.ts&quot;,
+     &quot;label&quot;: &quot;src\\index.ts&quot;,
+     &quot;value&quot;: &quot;src\\index.ts&quot;,
    },
  ]

 ❯ src/ui/hooks/useCompletion.integration.test.ts:337:40
            </failure>
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/useConsoleMessages.test.ts" timestamp="2025-07-02T12:17:37.839Z" hostname="Ajayk" tests="9" failures="0" errors="0" skipped="0" time="0.0761519">
        <testcase classname="src/ui/hooks/useConsoleMessages.test.ts" name="useConsoleMessages &gt; should initialize with an empty array of console messages" time="0.0304477">
        </testcase>
        <testcase classname="src/ui/hooks/useConsoleMessages.test.ts" name="useConsoleMessages &gt; should add a new message" time="0.0076099">
        </testcase>
        <testcase classname="src/ui/hooks/useConsoleMessages.test.ts" name="useConsoleMessages &gt; should consolidate identical consecutive messages" time="0.0048213">
        </testcase>
        <testcase classname="src/ui/hooks/useConsoleMessages.test.ts" name="useConsoleMessages &gt; should not consolidate different messages" time="0.005245">
        </testcase>
        <testcase classname="src/ui/hooks/useConsoleMessages.test.ts" name="useConsoleMessages &gt; should not consolidate messages if type is different" time="0.0058893">
        </testcase>
        <testcase classname="src/ui/hooks/useConsoleMessages.test.ts" name="useConsoleMessages &gt; should clear console messages" time="0.0061757">
        </testcase>
        <testcase classname="src/ui/hooks/useConsoleMessages.test.ts" name="useConsoleMessages &gt; should clear pending timeout on clearConsoleMessages" time="0.0043923">
        </testcase>
        <testcase classname="src/ui/hooks/useConsoleMessages.test.ts" name="useConsoleMessages &gt; should clear message queue on clearConsoleMessages" time="0.0035696">
        </testcase>
        <testcase classname="src/ui/hooks/useConsoleMessages.test.ts" name="useConsoleMessages &gt; should cleanup timeout on unmount" time="0.004141">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/useEditorSettings.test.ts" timestamp="2025-07-02T12:17:37.841Z" hostname="Ajayk" tests="10" failures="0" errors="0" skipped="0" time="0.1273479">
        <testcase classname="src/ui/hooks/useEditorSettings.test.ts" name="useEditorSettings &gt; should initialize with dialog closed" time="0.0348102">
        </testcase>
        <testcase classname="src/ui/hooks/useEditorSettings.test.ts" name="useEditorSettings &gt; should open editor dialog when openEditorDialog is called" time="0.0073987">
        </testcase>
        <testcase classname="src/ui/hooks/useEditorSettings.test.ts" name="useEditorSettings &gt; should close editor dialog when exitEditorDialog is called" time="0.0055549">
        </testcase>
        <testcase classname="src/ui/hooks/useEditorSettings.test.ts" name="useEditorSettings &gt; should handle editor selection successfully" time="0.0125752">
        </testcase>
        <testcase classname="src/ui/hooks/useEditorSettings.test.ts" name="useEditorSettings &gt; should handle clearing editor preference (undefined editor)" time="0.0237364">
        </testcase>
        <testcase classname="src/ui/hooks/useEditorSettings.test.ts" name="useEditorSettings &gt; should handle different editor types" time="0.0187173">
        </testcase>
        <testcase classname="src/ui/hooks/useEditorSettings.test.ts" name="useEditorSettings &gt; should handle different setting scopes" time="0.0047977">
        </testcase>
        <testcase classname="src/ui/hooks/useEditorSettings.test.ts" name="useEditorSettings &gt; should not set preference for unavailable editors" time="0.0049673">
        </testcase>
        <testcase classname="src/ui/hooks/useEditorSettings.test.ts" name="useEditorSettings &gt; should not set preference for editors not allowed in sandbox" time="0.0055101">
        </testcase>
        <testcase classname="src/ui/hooks/useEditorSettings.test.ts" name="useEditorSettings &gt; should handle errors during editor selection" time="0.0049816">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/useGitBranchName.test.ts" timestamp="2025-07-02T12:17:37.842Z" hostname="Ajayk" tests="7" failures="0" errors="0" skipped="2" time="0.086421">
        <testcase classname="src/ui/hooks/useGitBranchName.test.ts" name="useGitBranchName &gt; should return branch name" time="0.0516389">
        </testcase>
        <testcase classname="src/ui/hooks/useGitBranchName.test.ts" name="useGitBranchName &gt; should return undefined if git command fails" time="0.007008">
        </testcase>
        <testcase classname="src/ui/hooks/useGitBranchName.test.ts" name="useGitBranchName &gt; should return short commit hash if branch is HEAD (detached state)" time="0.0061382">
        </testcase>
        <testcase classname="src/ui/hooks/useGitBranchName.test.ts" name="useGitBranchName &gt; should return undefined if branch is HEAD and getting commit hash fails" time="0.0056113">
        </testcase>
        <testcase classname="src/ui/hooks/useGitBranchName.test.ts" name="useGitBranchName &gt; should update branch name when .git/HEAD changes" time="0.0018184">
            <skipped/>
        </testcase>
        <testcase classname="src/ui/hooks/useGitBranchName.test.ts" name="useGitBranchName &gt; should handle watcher setup error silently" time="0.0081764">
        </testcase>
        <testcase classname="src/ui/hooks/useGitBranchName.test.ts" name="useGitBranchName &gt; should cleanup watcher on unmount" time="0.0022184">
            <skipped/>
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/useHistoryManager.test.ts" timestamp="2025-07-02T12:17:37.843Z" hostname="Ajayk" tests="11" failures="1" errors="0" skipped="0" time="0.1064548">
        <testcase classname="src/ui/hooks/useHistoryManager.test.ts" name="useHistoryManager &gt; should initialize with an empty history" time="0.0367031">
        </testcase>
        <testcase classname="src/ui/hooks/useHistoryManager.test.ts" name="useHistoryManager &gt; should add an item to history with a unique ID" time="0.0078138">
        </testcase>
        <testcase classname="src/ui/hooks/useHistoryManager.test.ts" name="useHistoryManager &gt; should generate unique IDs for items added with the same base timestamp" time="0.0044438">
        </testcase>
        <testcase classname="src/ui/hooks/useHistoryManager.test.ts" name="useHistoryManager &gt; should update an existing history item" time="0.0050445">
        </testcase>
        <testcase classname="src/ui/hooks/useHistoryManager.test.ts" name="useHistoryManager &gt; should not change history if updateHistoryItem is called with a non-existent ID" time="0.0044148">
        </testcase>
        <testcase classname="src/ui/hooks/useHistoryManager.test.ts" name="useHistoryManager &gt; should clear the history" time="0.0057105">
        </testcase>
        <testcase classname="src/ui/hooks/useHistoryManager.test.ts" name="useHistoryManager &gt; should not add consecutive duplicate user messages" time="0.0041554">
        </testcase>
        <testcase classname="src/ui/hooks/useHistoryManager.test.ts" name="useHistoryManager &gt; should add duplicate user messages if they are not consecutive" time="0.0219097">
            <failure message="expected [ { type: &apos;user&apos;, …(2) }, …(1) ] to have a length of 3 but got 2" type="AssertionError">
AssertionError: expected [ { type: &apos;user&apos;, …(2) }, …(1) ] to have a length of 3 but got 2

- Expected
+ Received

- 3
+ 2

 ❯ src/ui/hooks/useHistoryManager.test.ts:197:36
            </failure>
        </testcase>
        <testcase classname="src/ui/hooks/useHistoryManager.test.ts" name="useHistoryManager &gt; should not add consecutive duplicate arien messages" time="0.0045301">
        </testcase>
        <testcase classname="src/ui/hooks/useHistoryManager.test.ts" name="useHistoryManager &gt; should not add consecutive duplicate arien_content messages" time="0.0042264">
        </testcase>
        <testcase classname="src/ui/hooks/useHistoryManager.test.ts" name="useHistoryManager &gt; should not add duplicate mixed arien/arien_content messages" time="0.0031542">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/useInputHistory.test.ts" timestamp="2025-07-02T12:17:37.845Z" hostname="Ajayk" tests="11" failures="0" errors="0" skipped="0" time="0.0848787">
        <testcase classname="src/ui/hooks/useInputHistory.test.ts" name="useInputHistory &gt; should initialize with historyIndex -1 and empty originalQueryBeforeNav" time="0.0297283">
        </testcase>
        <testcase classname="src/ui/hooks/useInputHistory.test.ts" name="useInputHistory &gt; handleSubmit &gt; should call onSubmit with trimmed value and reset history" time="0.0068439">
        </testcase>
        <testcase classname="src/ui/hooks/useInputHistory.test.ts" name="useInputHistory &gt; handleSubmit &gt; should not call onSubmit if value is empty after trimming" time="0.0051774">
        </testcase>
        <testcase classname="src/ui/hooks/useInputHistory.test.ts" name="useInputHistory &gt; navigateUp &gt; should not navigate if isActive is false" time="0.0043753">
        </testcase>
        <testcase classname="src/ui/hooks/useInputHistory.test.ts" name="useInputHistory &gt; navigateUp &gt; should not navigate if userMessages is empty" time="0.0042837">
        </testcase>
        <testcase classname="src/ui/hooks/useInputHistory.test.ts" name="useInputHistory &gt; navigateUp &gt; should call onChange with the last message when navigating up from initial state" time="0.0066865">
        </testcase>
        <testcase classname="src/ui/hooks/useInputHistory.test.ts" name="useInputHistory &gt; navigateUp &gt; should store currentQuery as originalQueryBeforeNav on first navigateUp" time="0.0053223">
        </testcase>
        <testcase classname="src/ui/hooks/useInputHistory.test.ts" name="useInputHistory &gt; navigateUp &gt; should navigate through history messages on subsequent navigateUp calls" time="0.005367">
        </testcase>
        <testcase classname="src/ui/hooks/useInputHistory.test.ts" name="useInputHistory &gt; navigateDown &gt; should not navigate if isActive is false" time="0.0050073">
        </testcase>
        <testcase classname="src/ui/hooks/useInputHistory.test.ts" name="useInputHistory &gt; navigateDown &gt; should not navigate if historyIndex is -1 (not in history navigation)" time="0.0038952">
        </testcase>
        <testcase classname="src/ui/hooks/useInputHistory.test.ts" name="useInputHistory &gt; navigateDown &gt; should restore originalQueryBeforeNav when navigating down to initial state" time="0.0040083">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/useLoadingIndicator.test.ts" timestamp="2025-07-02T12:17:37.847Z" hostname="Ajayk" tests="5" failures="0" errors="0" skipped="0" time="0.0868267">
        <testcase classname="src/ui/hooks/useLoadingIndicator.test.ts" name="useLoadingIndicator &gt; should initialize with default values when Idle" time="0.0368754">
        </testcase>
        <testcase classname="src/ui/hooks/useLoadingIndicator.test.ts" name="useLoadingIndicator &gt; should reflect values when Responding" time="0.0141737">
        </testcase>
        <testcase classname="src/ui/hooks/useLoadingIndicator.test.ts" name="useLoadingIndicator &gt; should show waiting phrase and retain elapsedTime when WaitingForConfirmation" time="0.0118752">
        </testcase>
        <testcase classname="src/ui/hooks/useLoadingIndicator.test.ts" name="useLoadingIndicator &gt; should reset elapsedTime and use a witty phrase when transitioning from WaitingForConfirmation to Responding" time="0.010882">
        </testcase>
        <testcase classname="src/ui/hooks/useLoadingIndicator.test.ts" name="useLoadingIndicator &gt; should reset timer and phrase when streamingState changes from Responding to Idle" time="0.008966">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/usePhraseCycler.test.ts" timestamp="2025-07-02T12:17:37.847Z" hostname="Ajayk" tests="7" failures="0" errors="0" skipped="0" time="0.0827563">
        <testcase classname="src/ui/hooks/usePhraseCycler.test.ts" name="usePhraseCycler &gt; should initialize with the first witty phrase when not active and not waiting" time="0.0394203">
        </testcase>
        <testcase classname="src/ui/hooks/usePhraseCycler.test.ts" name="usePhraseCycler &gt; should show &quot;Waiting for user confirmation...&quot; when isWaiting is true" time="0.0079448">
        </testcase>
        <testcase classname="src/ui/hooks/usePhraseCycler.test.ts" name="usePhraseCycler &gt; should not cycle phrases if isActive is false and not waiting" time="0.004102">
        </testcase>
        <testcase classname="src/ui/hooks/usePhraseCycler.test.ts" name="usePhraseCycler &gt; should cycle through witty phrases when isActive is true and not waiting" time="0.0064373">
        </testcase>
        <testcase classname="src/ui/hooks/usePhraseCycler.test.ts" name="usePhraseCycler &gt; should reset to a witty phrase when isActive becomes true after being false (and not waiting)" time="0.0114729">
        </testcase>
        <testcase classname="src/ui/hooks/usePhraseCycler.test.ts" name="usePhraseCycler &gt; should clear phrase interval on unmount when active" time="0.003798">
        </testcase>
        <testcase classname="src/ui/hooks/usePhraseCycler.test.ts" name="usePhraseCycler &gt; should reset to a witty phrase when transitioning from waiting to active" time="0.0055209">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/useShellHistory.test.ts" timestamp="2025-07-02T12:17:37.848Z" hostname="Ajayk" tests="7" failures="0" errors="0" skipped="0" time="0.9025796">
        <testcase classname="src/ui/hooks/useShellHistory.test.ts" name="useShellHistory &gt; should initialize and read the history file from the correct path" time="0.1111629">
        </testcase>
        <testcase classname="src/ui/hooks/useShellHistory.test.ts" name="useShellHistory &gt; should handle a non-existent history file gracefully" time="0.0968017">
        </testcase>
        <testcase classname="src/ui/hooks/useShellHistory.test.ts" name="useShellHistory &gt; should add a command and write to the history file" time="0.1689056">
        </testcase>
        <testcase classname="src/ui/hooks/useShellHistory.test.ts" name="useShellHistory &gt; should navigate history correctly with previous/next commands" time="0.0756825">
        </testcase>
        <testcase classname="src/ui/hooks/useShellHistory.test.ts" name="useShellHistory &gt; should not add empty or whitespace-only commands to history" time="0.1010348">
        </testcase>
        <testcase classname="src/ui/hooks/useShellHistory.test.ts" name="useShellHistory &gt; should truncate history to MAX_HISTORY_LENGTH (100)" time="0.1844661">
        </testcase>
        <testcase classname="src/ui/hooks/useShellHistory.test.ts" name="useShellHistory &gt; should move an existing command to the top when re-added" time="0.1610761">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/useTimer.test.ts" timestamp="2025-07-02T12:17:37.850Z" hostname="Ajayk" tests="8" failures="0" errors="0" skipped="0" time="0.0892899">
        <testcase classname="src/ui/hooks/useTimer.test.ts" name="useTimer &gt; should initialize with 0" time="0.0388454">
        </testcase>
        <testcase classname="src/ui/hooks/useTimer.test.ts" name="useTimer &gt; should not increment time if isActive is false" time="0.0047736">
        </testcase>
        <testcase classname="src/ui/hooks/useTimer.test.ts" name="useTimer &gt; should increment time every second if isActive is true" time="0.0083654">
        </testcase>
        <testcase classname="src/ui/hooks/useTimer.test.ts" name="useTimer &gt; should reset to 0 and start incrementing when isActive becomes true from false" time="0.0132722">
        </testcase>
        <testcase classname="src/ui/hooks/useTimer.test.ts" name="useTimer &gt; should reset to 0 when resetKey changes while active" time="0.0047414">
        </testcase>
        <testcase classname="src/ui/hooks/useTimer.test.ts" name="useTimer &gt; should be 0 if isActive is false, regardless of resetKey changes" time="0.0032063">
        </testcase>
        <testcase classname="src/ui/hooks/useTimer.test.ts" name="useTimer &gt; should clear timer on unmount" time="0.0060595">
        </testcase>
        <testcase classname="src/ui/hooks/useTimer.test.ts" name="useTimer &gt; should preserve elapsedTime when isActive becomes false, and reset to 0 when it becomes active again" time="0.0055663">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/useToolScheduler.test.ts" timestamp="2025-07-02T12:17:37.851Z" hostname="Ajayk" tests="21" failures="0" errors="0" skipped="4" time="0.2634573">
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="useReactToolScheduler in YOLO Mode &gt; should skip confirmation and execute tool directly when yoloMode is true" time="0.1407514">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="useReactToolScheduler &gt; initial state should be empty" time="0.0136705">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="useReactToolScheduler &gt; should schedule and execute a tool call successfully" time="0.0206607">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="useReactToolScheduler &gt; should handle tool not found" time="0.0195665">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="useReactToolScheduler &gt; should handle error during shouldConfirmExecute" time="0.0155103">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="useReactToolScheduler &gt; should handle error during execute" time="0.0107312">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="useReactToolScheduler &gt; should handle tool requiring confirmation - approved" time="0">
            <skipped/>
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="useReactToolScheduler &gt; should handle tool requiring confirmation - cancelled by user" time="0">
            <skipped/>
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="useReactToolScheduler &gt; should handle live output updates" time="0">
            <skipped/>
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="useReactToolScheduler &gt; should schedule and execute multiple tool calls" time="0.0298085">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="useReactToolScheduler &gt; should throw error if scheduling while already running" time="0">
            <skipped/>
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="mapToDisplay &gt; should map ToolCall with status &apos;validating&apos; (validating) correctly" time="0.0018103">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="mapToDisplay &gt; should map ToolCall with status &apos;awaiting_approval&apos; (awaiting_approval) correctly" time="0.0005502">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="mapToDisplay &gt; should map ToolCall with status &apos;scheduled&apos; (scheduled) correctly" time="0.0005403">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="mapToDisplay &gt; should map ToolCall with status &apos;executing&apos; (executing no live output) correctly" time="0.0004867">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="mapToDisplay &gt; should map ToolCall with status &apos;executing&apos; (executing with live output) correctly" time="0.0005026">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="mapToDisplay &gt; should map ToolCall with status &apos;success&apos; (success) correctly" time="0.0004902">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="mapToDisplay &gt; should map ToolCall with status &apos;error&apos; (error tool not found) correctly" time="0.0005569">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="mapToDisplay &gt; should map ToolCall with status &apos;error&apos; (error tool execution failed) correctly" time="0.0004289">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="mapToDisplay &gt; should map ToolCall with status &apos;cancelled&apos; (cancelled) correctly" time="0.0005647">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="mapToDisplay &gt; should map an array of ToolCalls correctly" time="0.0013931">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/messages/ArienMessage.test.tsx" timestamp="2025-07-02T12:17:37.854Z" hostname="Ajayk" tests="6" failures="1" errors="0" skipped="0" time="0.0906733">
        <testcase classname="src/ui/components/messages/ArienMessage.test.tsx" name="ArienMessage &gt; should render without crashing" time="0.0319508">
        </testcase>
        <testcase classname="src/ui/components/messages/ArienMessage.test.tsx" name="ArienMessage &gt; should display message text in MarkdownDisplay" time="0.0133679">
        </testcase>
        <testcase classname="src/ui/components/messages/ArienMessage.test.tsx" name="ArienMessage &gt; should show animated icon with pending state" time="0.0142388">
        </testcase>
        <testcase classname="src/ui/components/messages/ArienMessage.test.tsx" name="ArienMessage &gt; should show animated icon with non-pending state" time="0.0175093">
            <failure message="expected &apos;🤖&apos; to be &apos;&apos; // Object.is equality" type="AssertionError">
AssertionError: expected &apos;🤖&apos; to be &apos;&apos; // Object.is equality

- Expected
+ Received

+ 🤖

 ❯ src/ui/components/messages/ArienMessage.test.tsx:72:38
            </failure>
        </testcase>
        <testcase classname="src/ui/components/messages/ArienMessage.test.tsx" name="ArienMessage &gt; should pass availableTerminalHeight to MarkdownDisplay when provided" time="0.0055251">
        </testcase>
        <testcase classname="src/ui/components/messages/ArienMessage.test.tsx" name="ArienMessage &gt; should pass terminalWidth to MarkdownDisplay" time="0.0044084">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/messages/DiffRenderer.test.tsx" timestamp="2025-07-02T12:17:37.855Z" hostname="Ajayk" tests="13" failures="0" errors="0" skipped="0" time="0.4187258">
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should call colorizeCode with correct language for new file with known extension" time="0.047182">
        </testcase>
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should call colorizeCode with null language for new file with unknown extension" time="0.1675969">
        </testcase>
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should call colorizeCode with null language for new file if no filename is provided" time="0.023415">
        </testcase>
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should render diff content for existing file (not calling colorizeCode directly for the whole block)" time="0.0172081">
        </testcase>
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should handle diff with only header and no changes" time="0.0048362">
        </testcase>
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should handle empty diff content" time="0.0027911">
        </testcase>
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should render a gap indicator for skipped lines" time="0.0207773">
        </testcase>
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should not render a gap indicator for small gaps (&lt;= MAX_CONTEXT_LINES_WITHOUT_GAP)" time="0.026065">
        </testcase>
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should correctly render a diff with multiple hunks and a gap indicator &gt; with terminalWidth 80 and height undefined" time="0.0230563">
        </testcase>
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should correctly render a diff with multiple hunks and a gap indicator &gt; with terminalWidth 80 and height 6" time="0.0221862">
        </testcase>
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should correctly render a diff with multiple hunks and a gap indicator &gt; with terminalWidth 30 and height 6" time="0.0177058">
        </testcase>
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should correctly render a diff with a SVN diff format" time="0.0178082">
        </testcase>
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should correctly render a new file with no file extension correctly" time="0.0240042">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/messages/ToolConfirmationMessage.test.tsx" timestamp="2025-07-02T12:17:37.857Z" hostname="Ajayk" tests="2" failures="0" errors="0" skipped="0" time="0.0857024">
        <testcase classname="src/ui/components/messages/ToolConfirmationMessage.test.tsx" name="ToolConfirmationMessage &gt; should not display urls if prompt and url are the same" time="0.0652739">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolConfirmationMessage.test.tsx" name="ToolConfirmationMessage &gt; should display urls if prompt and url are different" time="0.0172682">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/messages/ToolMessage.test.tsx" timestamp="2025-07-02T12:17:37.857Z" hostname="Ajayk" tests="16" failures="0" errors="0" skipped="0" time="0.2119987">
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; renders basic tool information" time="0.0684792">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; ToolStatusIndicator rendering &gt; shows ✓ for Success status" time="0.0098697">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; ToolStatusIndicator rendering &gt; shows ◦ for Pending status" time="0.0084108">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; ToolStatusIndicator rendering &gt; shows ⚡ for Confirming status" time="0.0063113">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; ToolStatusIndicator rendering &gt; shows ⊘ for Canceled status" time="0.0066008">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; ToolStatusIndicator rendering &gt; shows ✗ for Error status" time="0.0061765">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; ToolStatusIndicator rendering &gt; shows executing symbol for Executing status when streamingState is Idle" time="0.0073654">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; ToolStatusIndicator rendering &gt; shows executing symbol for Executing status when streamingState is WaitingForConfirmation" time="0.0087753">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; ToolStatusIndicator rendering &gt; shows MockRespondingSpinner for Executing status when streamingState is Responding" time="0.0083728">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; renders DiffRenderer for diff results" time="0.0082492">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; renders emphasis correctly" time="0.0167901">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; handles long result display by truncating" time="0.0064739">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; renders without result display" time="0.0078093">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; respects availableTerminalHeight constraint" time="0.0161901">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; handles empty result display" time="0.0046762">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; handles different terminal widths" time="0.0160608">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/shared/MaxSizedBox.test.tsx" timestamp="2025-07-02T12:17:37.859Z" hostname="Ajayk" tests="16" failures="0" errors="0" skipped="0" time="0.215427">
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; renders children without truncation when they fit" time="0.0500313">
            <system-err>
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;

            </system-err>
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; hides lines when content exceeds maxHeight" time="0.009378">
            <system-err>
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;

            </system-err>
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; hides lines at the end when content exceeds maxHeight and overflowDirection is bottom" time="0.0063341">
            <system-err>
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;

            </system-err>
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; wraps text that exceeds maxWidth" time="0.0074361">
            <system-err>
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;

            </system-err>
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; handles mixed wrapping and non-wrapping segments" time="0.024251">
            <system-err>
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;

            </system-err>
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; handles words longer than maxWidth by splitting them" time="0.0107128">
            <system-err>
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;

            </system-err>
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; does not truncate when maxHeight is undefined" time="0.0046312">
            <system-err>
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;

            </system-err>
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; shows plural &quot;lines&quot; when more than one line is hidden" time="0.0063364">
            <system-err>
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;

            </system-err>
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; shows plural &quot;lines&quot; when more than one line is hidden and overflowDirection is bottom" time="0.0057985">
            <system-err>
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;

            </system-err>
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; renders an empty box for empty children" time="0.0029844">
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; wraps text with multi-byte unicode characters correctly" time="0.0104828">
            <system-err>
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;

            </system-err>
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; wraps text with multi-byte emoji characters correctly" time="0.0237688">
            <system-err>
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;

            </system-err>
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; accounts for additionalHiddenLinesCount" time="0.0065749">
            <system-err>
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;

            </system-err>
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; handles React.Fragment as a child" time="0.0075146">
            <system-err>
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;

            </system-err>
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; clips a long single text child from the top" time="0.015093">
            <system-err>
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;

            </system-err>
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; clips a long single text child from the bottom" time="0.0190608">
            <system-err>
MaxSizedBox children must have flexDirection=&quot;row&quot;. [object Object]. Source: &lt;Unknown file&gt;

            </system-err>
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/shared/text-buffer.test.ts" timestamp="2025-07-02T12:17:37.864Z" hostname="Ajayk" tests="60" failures="0" errors="0" skipped="0" time="0.7965438">
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Initialization &gt; should initialize with empty text and cursor at (0,0) by default" time="0.0429938">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Initialization &gt; should initialize with provided initialText" time="0.00699">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Initialization &gt; should initialize with initialText and initialCursorOffset" time="0.0055354">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Initialization &gt; should wrap visual lines" time="0.0059165">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Initialization &gt; should wrap visual lines with multiple spaces" time="0.0272681">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Initialization &gt; should wrap visual lines even without spaces" time="0.0079187">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Initialization &gt; should initialize with multi-byte unicode characters and correct cursor offset" time="0.0094597">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Basic Editing &gt; insert: should insert a character and update cursor" time="0.0118099">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Basic Editing &gt; insert: should insert text in the middle of a line" time="0.026514">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Basic Editing &gt; newline: should create a new line and move cursor" time="0.0675401">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Basic Editing &gt; backspace: should delete char to the left or merge lines" time="0.0089128">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Basic Editing &gt; del: should delete char to the right or merge lines" time="0.0078944">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Drag and Drop File Paths &gt; should prepend @ to a valid file path on insert" time="0.0068603">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Drag and Drop File Paths &gt; should not prepend @ to an invalid file path on insert" time="0.0254327">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Drag and Drop File Paths &gt; should handle quoted paths" time="0.0091145">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Drag and Drop File Paths &gt; should not prepend @ to short text that is not a path" time="0.035173">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Cursor Movement &gt; move: left/right should work within and across visual lines (due to wrapping)" time="0.0302599">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Cursor Movement &gt; move: up/down should preserve preferred visual column" time="0.0136361">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Cursor Movement &gt; move: home/end should go to visual line start/end" time="0.0108215">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Visual Layout &amp; Viewport &gt; should wrap long lines correctly into visualLines" time="0.033901">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Visual Layout &amp; Viewport &gt; should update visualScrollRow when visualCursor moves out of viewport" time="0.0635302">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Undo/Redo &gt; should undo and redo an insert operation" time="0.0095855">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Undo/Redo &gt; should undo and redo a newline operation" time="0.012704">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Unicode Handling &gt; insert: should correctly handle multi-byte unicode characters" time="0.0085707">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Unicode Handling &gt; backspace: should correctly delete multi-byte unicode characters" time="0.0067788">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Unicode Handling &gt; move: left/right should treat multi-byte chars as single units for visual cursor" time="0.0058784">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; handleInput &gt; should insert printable characters" time="0.0139905">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; handleInput &gt; should handle &quot;Enter&quot; key as newline" time="0.0051142">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; handleInput &gt; should handle &quot;Backspace&quot; key" time="0.0085998">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; handleInput &gt; should handle multiple delete characters in one input" time="0.0342875">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; handleInput &gt; should handle inserts that contain delete characters " time="0.0069288">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; handleInput &gt; should handle inserts with a mix of regular and delete characters " time="0.0067799">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; handleInput &gt; should handle arrow keys for movement" time="0.0074394">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; handleInput &gt; should strip ANSI escape codes when pasting text" time="0.0069448">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; handleInput &gt; should handle VSCode terminal Shift+Enter as newline" time="0.0050032">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; handleInput &gt; should correctly handle repeated pasting of long text" time="0.1040366">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; replaceRange &gt; should replace a single-line range with single-line text" time="0.0065439">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; replaceRange &gt; should replace a multi-line range with single-line text" time="0.0052965">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; replaceRange &gt; should delete a range when replacing with an empty string" time="0.004422">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; replaceRange &gt; should handle replacing at the beginning of the text" time="0.0053411">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; replaceRange &gt; should handle replacing at the end of the text" time="0.0047257">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; replaceRange &gt; should handle replacing the entire buffer content" time="0.0056546">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; replaceRange &gt; should correctly replace with unicode characters" time="0.0068549">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; replaceRange &gt; should handle invalid range by returning false and not changing text" time="0.0094837">
            <system-err>
Invalid range provided to replaceRange {
  startRow: [33m0[39m,
  startCol: [33m5[39m,
  endRow: [33m0[39m,
  endCol: [33m3[39m,
  linesLength: [33m1[39m,
  endRowLineLength: [33m4[39m
}
Invalid range provided to replaceRange {
  startRow: [33m1[39m,
  startCol: [33m0[39m,
  endRow: [33m0[39m,
  endCol: [33m0[39m,
  linesLength: [33m1[39m,
  endRowLineLength: [33m4[39m
}

            </system-err>
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; replaceRange &gt; replaceRange: multiple lines with a single character" time="0.0082086">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Input Sanitization &gt; should strip ANSI escape codes from input" time="0.0061826">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Input Sanitization &gt; should strip control characters from input" time="0.0047797">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Input Sanitization &gt; should strip mixed ANSI and control characters from input" time="0.0044338">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Input Sanitization &gt; should not strip standard characters or newlines" time="0.0054391">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Input Sanitization &gt; should sanitize pasted text via handleInput" time="0.0043829">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="offsetToLogicalPos &gt; should return [0,0] for offset 0" time="0.0005104">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="offsetToLogicalPos &gt; should handle single line text" time="0.0010207">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="offsetToLogicalPos &gt; should handle multi-line text" time="0.0015075">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="offsetToLogicalPos &gt; should handle empty lines" time="0.0007667">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="offsetToLogicalPos &gt; should handle text ending with a newline" time="0.0007732">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="offsetToLogicalPos &gt; should handle text starting with a newline" time="0.0006075">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="offsetToLogicalPos &gt; should handle empty string input" time="0.0004631">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="offsetToLogicalPos &gt; should handle multi-byte unicode characters correctly" time="0.000835">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="offsetToLogicalPos &gt; should handle offset exactly at newline character" time="0.0006529">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="offsetToLogicalPos &gt; should handle offset in the middle of a multi-byte character (should place at start of that char)" time="0.0007436">
        </testcase>
    </testsuite>
</testsuites>
