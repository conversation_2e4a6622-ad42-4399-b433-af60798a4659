{"version": "3.2.4", "results": [[":src/ui/hooks/slashCommandProcessor.test.ts", {"duration": 399.40149999999994, "failed": true}], [":src/ui/components/shared/text-buffer.test.ts", {"duration": 796.5438000000004, "failed": false}], [":src/ui/hooks/useArienStream.test.tsx", {"duration": 485.0517000000009, "failed": false}], [":src/ui/hooks/useToolScheduler.test.ts", {"duration": 263.45730000000003, "failed": false}], [":src/ui/hooks/atCommandProcessor.test.ts", {"duration": 98.85110000000077, "failed": true}], [":src/config/settings.test.ts", {"duration": 50.57379999999921, "failed": false}], [":src/config/config.test.ts", {"duration": 47918.5441, "failed": true}], [":src/ui/App.test.tsx", {"duration": 951.9562999999998, "failed": true}], [":src/ui/hooks/useCompletion.integration.test.ts", {"duration": 1535.5429999999997, "failed": true}], [":src/ui/components/messages/DiffRenderer.test.tsx", {"duration": 418.72580000000016, "failed": false}], [":src/ui/hooks/useAutoAcceptIndicator.test.ts", {"duration": 72.65850000000046, "failed": false}], [":src/ui/components/shared/MaxSizedBox.test.tsx", {"duration": 215.42700000000013, "failed": false}], [":src/nonInteractiveCli.test.ts", {"duration": 26.63799999999992, "failed": false}], [":src/ui/components/messages/ToolMessage.test.tsx", {"duration": 211.9987000000001, "failed": false}], [":src/ui/hooks/useHistoryManager.test.ts", {"duration": 106.45479999999998, "failed": true}], [":src/config/mcp-integration.test.ts", {"duration": 15151.597099999999, "failed": true}], [":src/ui/hooks/useEditorSettings.test.ts", {"duration": 127.34789999999975, "failed": false}], [":src/ui/contexts/SessionContext.test.tsx", {"duration": 115.53279999999995, "failed": false}], [":src/ui/hooks/useInputHistory.test.ts", {"duration": 84.87869999999975, "failed": false}], [":src/ui/hooks/useGitBranchName.test.ts", {"duration": 86.42099999999982, "failed": false}], [":src/config/config.integration.test.ts", {"duration": 119.35030000000006, "failed": false}], [":src/ui/hooks/useShellHistory.test.ts", {"duration": 902.5796, "failed": false}], [":src/config/built-in-mcp-servers.test.ts", {"duration": 43.85279999999966, "failed": true}], [":src/ui/components/LoadingIndicator.test.tsx", {"duration": 125.28099999999995, "failed": false}], [":src/ui/components/InputPrompt.test.tsx", {"duration": 867.9775, "failed": false}], [":src/ui/hooks/useConsoleMessages.test.ts", {"duration": 76.15189999999984, "failed": false}], [":src/ui/hooks/usePhraseCycler.test.ts", {"duration": 82.75630000000001, "failed": false}], [":src/ui/hooks/shellCommandProcessor.test.ts", {"duration": 62.9681999999998, "failed": false}], [":src/ui/utils/errorParsing.test.ts", {"duration": 7.232899999999972, "failed": false}], [":src/ui/hooks/useLoadingIndicator.test.ts", {"duration": 86.82670000000007, "failed": false}], [":src/arien.test.tsx", {"duration": 7.463999999999942, "failed": false}], [":src/ui/hooks/useTimer.test.ts", {"duration": 89.28990000000022, "failed": false}], [":src/config/extension.test.ts", {"duration": 37.17340000000013, "failed": false}], [":src/ui/components/AuthDialog.test.tsx", {"duration": 352.9920000000002, "failed": false}], [":src/ui/components/messages/ArienMessage.test.tsx", {"duration": 90.67330000000038, "failed": true}], [":src/ui/components/HistoryItemDisplay.test.tsx", {"duration": 202.54979999999978, "failed": true}], [":src/utils/startupWarnings.test.ts", {"duration": 0, "failed": false}], [":src/ui/components/Stats.test.tsx", {"duration": 115.98840000000018, "failed": false}], [":src/ui/utils/formatters.test.ts", {"duration": 12.755599999999959, "failed": false}], [":src/ui/utils/markdownUtilities.test.ts", {"duration": 8.497899999999845, "failed": false}], [":src/ui/components/StatsDisplay.test.tsx", {"duration": 123.00409999999965, "failed": false}], [":src/ui/components/messages/ToolConfirmationMessage.test.tsx", {"duration": 85.70240000000013, "failed": false}], [":src/ui/components/SessionSummaryDisplay.test.tsx", {"duration": 133.4976999999999, "failed": false}], [":src/ui/utils/textUtils.test.ts", {"duration": 7.5634000000000015, "failed": false}]]}