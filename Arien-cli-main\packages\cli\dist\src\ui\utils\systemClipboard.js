/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import clipboardy from 'clipboardy';
import { execSync } from 'child_process';
import os from 'os';
/**
 * System clipboard utility that provides safe access to the system clipboard
 * with proper error handling and fallback behavior.
 */
export class SystemClipboard {
    static instance = null;
    isAvailable = true;
    constructor() {
        // Test clipboard availability on initialization (async, but don't wait)
        this.testClipboardAvailability().catch(() => {
            // Silently fail - availability will be false
        });
    }
    /**
     * Get the singleton instance of SystemClipboard
     */
    static getInstance() {
        if (!SystemClipboard.instance) {
            SystemClipboard.instance = new SystemClipboard();
        }
        return SystemClipboard.instance;
    }
    /**
     * Test if the system clipboard is available
     */
    async testClipboardAvailability() {
        try {
            // Try to write a test string first, then read it back
            // This handles the case where clipboard might be empty initially
            const testString = 'test';
            await clipboardy.write(testString);
            const result = await clipboardy.read();
            this.isAvailable = result === testString;
        }
        catch (error) {
            // Silently fail - clipboard might not be available in some environments
            this.isAvailable = false;
        }
    }
    /**
     * Write text to the system clipboard
     * @param text The text to write to clipboard
     * @returns Promise<boolean> True if successful, false otherwise
     */
    async writeText(text) {
        if (!this.isAvailable) {
            return false;
        }
        try {
            // Try Windows PowerShell first on Windows
            if (os.platform() === 'win32') {
                try {
                    execSync(`powershell -command "Set-Clipboard -Value '${text.replace(/'/g, "''")}'"`);
                    return true;
                }
                catch (winError) {
                    // Fall back to clipboardy
                }
            }
            await clipboardy.write(text);
            return true;
        }
        catch (error) {
            // Silently fail - don't spam console with clipboard errors
            this.isAvailable = false;
            return false;
        }
    }
    /**
     * Read text from the system clipboard
     * @returns Promise<string | null> The clipboard text or null if unavailable/error
     */
    async readText() {
        if (!this.isAvailable) {
            return null;
        }
        try {
            // Try Windows PowerShell first on Windows
            if (os.platform() === 'win32') {
                try {
                    const result = execSync('powershell -command "Get-Clipboard"', { encoding: 'utf8' });
                    return result.trim();
                }
                catch (winError) {
                    // Fall back to clipboardy
                }
            }
            const text = await clipboardy.read();
            return text;
        }
        catch (error) {
            // Silently fail - don't spam console with clipboard errors
            this.isAvailable = false;
            return null;
        }
    }
    /**
     * Check if the system clipboard is available
     * @returns boolean True if clipboard is available
     */
    isClipboardAvailable() {
        return this.isAvailable;
    }
    /**
     * Retry clipboard availability test
     */
    async retryAvailabilityTest() {
        await this.testClipboardAvailability();
    }
}
/**
 * Get the singleton system clipboard instance
 */
export const systemClipboard = SystemClipboard.getInstance();
//# sourceMappingURL=systemClipboard.js.map