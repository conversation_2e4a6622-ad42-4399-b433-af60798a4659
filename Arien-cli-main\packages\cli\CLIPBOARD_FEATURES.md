# Clipboard Integration Features

This document describes the enhanced copy/paste functionality implemented in Arien AI CLI.

## Overview

The Arien AI CLI now includes full system clipboard integration, allowing users to copy and paste text between the terminal interface and external applications.

## Features

### System Clipboard Integration
- **Cross-platform support**: Works on Windows, macOS, and Linux
- **Bidirectional sync**: Copy from Arien AI to system clipboard and paste from system clipboard to Arien AI
- **Graceful fallback**: Falls back to internal clipboard if system clipboard is unavailable

### Keyboard Shortcuts

#### Copy (Ctrl+C)
- **When text is selected**: Copies the selected text to both internal and system clipboard
- **When no text is selected**: Triggers the exit behavior (existing functionality preserved)

#### Paste (Ctrl+V)
- Pastes content from system clipboard into the input prompt
- Falls back to internal clipboard if system clipboard is empty or unavailable

#### Select All (Ctrl+A)
- Selects all text in the input prompt
- Useful for quickly copying entire input content

### Bracketed Paste Support
- **Right-click paste**: Supports terminal right-click paste operations
- **Middle-click paste**: Supports terminal middle-click paste (on supported terminals)
- **Large text handling**: Efficiently handles pasting of large text blocks

## Technical Implementation

### Components

1. **SystemClipboard Utility** (`src/ui/utils/systemClipboard.ts`)
   - Singleton class managing system clipboard access
   - Error handling and availability detection
   - Cross-platform clipboard operations using `clipboardy` library

2. **TextBuffer Integration** (`src/ui/components/shared/text-buffer.ts`)
   - Enhanced `copy()` and `paste()` methods with async system clipboard support
   - Maintains backward compatibility with internal clipboard
   - Automatic fallback mechanisms

3. **Keyboard Handler Updates** (`src/ui/App.tsx`)
   - Smart Ctrl+C behavior (copy vs exit)
   - Ctrl+V paste functionality
   - Ctrl+A select all functionality

### Dependencies
- `clipboardy`: Cross-platform clipboard access library
- Existing bracketed paste infrastructure

## Usage Examples

### Basic Copy/Paste
1. Type some text in the input prompt
2. Press `Ctrl+A` to select all text
3. Press `Ctrl+C` to copy to clipboard
4. The text is now available in your system clipboard
5. Press `Ctrl+V` to paste (or paste in external applications)

### Cross-Application Workflow
1. Copy text from a web browser or text editor
2. Switch to Arien AI terminal
3. Press `Ctrl+V` to paste the external content
4. Edit and use the content in your Arien AI session

### Terminal Paste Operations
- Right-click in terminal and select "Paste" (if supported)
- Middle-click to paste (on Linux/Unix terminals)
- Use terminal's native paste shortcuts

## Error Handling

The clipboard integration includes robust error handling:

- **Clipboard unavailable**: Falls back to internal clipboard operations
- **Permission errors**: Gracefully degrades without breaking functionality
- **Platform differences**: Handles platform-specific clipboard behaviors
- **Silent failures**: Avoids spamming console with clipboard errors

## Backward Compatibility

All existing functionality is preserved:
- Internal clipboard still works for copy/paste within the same session
- Ctrl+C still exits when no text is selected
- Bracketed paste continues to work as before
- All existing keyboard shortcuts remain functional

## Troubleshooting

### Clipboard Not Working
- Ensure you're running in a proper terminal environment
- Check that your system allows clipboard access for Node.js applications
- Try using the internal clipboard (copy/paste within the same session)

### Performance Issues
- Large clipboard content is handled efficiently
- System clipboard operations are asynchronous and non-blocking
- Fallback to internal clipboard if system operations are slow

## Future Enhancements

Potential future improvements:
- Rich text clipboard support
- Clipboard history
- Selective paste options
- Integration with external clipboard managers
